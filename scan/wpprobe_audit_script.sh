#!/bin/bash

WEB_MACHINE="http://localhost:3000"
LOGIC_MACHINE="http://localhost:5678"
# Konfiguracja
API_BASE_ENDPOINT="${WEB_MACHINE}/api/domains/get-domain"
WEBHOOK_URL="${LOGIC_MACHINE}/webhook/62168428-33bb-4289-9600-3c3ce928a383"
TEST_WEEBHOOK_URL="${LOGIC_MACHINE}/webhook-test/62168428-33bb-4289-9600-3c3ce928a383"
WP_PLUGIN_API="https://api.wordpress.org/plugins/info/1.0"
LOG_FILE="/home/<USER>/Projects/ai-studio/scan/audits/pprobe_scan_$(date +%Y%m%d_%H%M%S).log"

# Zmienne dla parametrów
API_PARAMS=""
TEST_MODE=false
SKIP_LIGHTHOUSE=false

# Funkcja wyświetlania pomocy
show_help() {
  echo "Użycie: $0 [OPCJE] [PARAMETRY_API]"
  echo ""
  echo "OPCJE:"
  echo "  -t, --test              Uruchom w trybie testowym"
  echo "  -s, --skip-lighthouse   Pomiń skanowanie Lighthouse"
  echo "  -h, --help              Wyświetl tę pomoc"
  echo ""
  echo "PARAMETRY_API:"
  echo "  Dodatkowe parametry URL do API endpoint (np. 'limit=10&status=active')"**************
  echo ""
  echo "Przykłady:"
  echo "  $0                                    # Podstawowe uruchomienie"
  echo "  $0 --test                            # Tryb testowy"
  echo "  $0 'limit=5&status=pending'          # Z parametrami API"
  echo "  $0 --test 'limit=10' --skip-lighthouse  # Kombinacja opcji"
}

# Przetwarzanie argumentów
while [[ $# -gt 0 ]]; do
  case $1 in
    --test|-t)
      TEST_MODE=true
      shift
      ;;
    --skip-lighthouse|-s)
      SKIP_LIGHTHOUSE=true
      shift
      ;;
    --help|-h)
      show_help
      exit 0
      ;;
    --*)
      echo "Nieznana opcja: $1"
      show_help
      exit 1
      ;;
    *)
      # Jeśli argument nie zaczyna się od --, traktuj jako parametry API
      if [ -z "$API_PARAMS" ]; then
        API_PARAMS="$1"
      else
        API_PARAMS="$API_PARAMS&$1"
      fi
      shift
      ;;
  esac
done

# Budowanie końcowego API endpoint
if [ -n "$API_PARAMS" ]; then
  API_ENDPOINT="${API_BASE_ENDPOINT}?${API_PARAMS}"
else
  API_ENDPOINT="$API_BASE_ENDPOINT"
fi

# Ustawienie odpowiedniego URL webhooka
if [ "$TEST_MODE" = true ]; then
  CURRENT_WEBHOOK_URL="$TEST_WEEBHOOK_URL"
else
  CURRENT_WEBHOOK_URL="$WEBHOOK_URL"
fi

# Funkcja logowania
log() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Funkcja pobierania najnowszej wersji pluginu z WordPress API
get_plugin_latest_version() {
  local plugin_slug="$1"

  if [ -z "$plugin_slug" ]; then
    echo "unknown"
    return 1
  fi

  log "Pobieranie danych dla pluginu: $plugin_slug z ${WP_PLUGIN_API}/${plugin_slug}.json" >&2

  local response=$(curl -s -w "%{http_code}" "${WP_PLUGIN_API}/${plugin_slug}.json")
  local http_code="${response: -3}"
  local response_body="${response%???}"

  if [ "$http_code" -eq 200 ]; then
    # Sprawdzamy czy odpowiedź jest prawidłowym JSON
    if echo "$response_body" | jq . >/dev/null 2>&1; then
      local version=$(echo "$response_body" | jq -r '.version // "unknown"')
      log "Pobrano wersję $version dla pluginu $plugin_slug" >&2
      echo "$version"
    else
      log "ERROR: Nieprawidłowy JSON w odpowiedzi dla pluginu $plugin_slug" >&2
      echo "unknown"
    fi
  else
    log "ERROR: HTTP $http_code dla pluginu $plugin_slug" >&2
    echo "unknown"
  fi
}

# Funkcja przetwarzania CSV i pobierania wersji pluginów
process_csv_and_get_versions() {
  local csv_file="$1"
  local temp_plugins_file="/tmp/plugins_info_$$.json"
  echo "[]" >"$temp_plugins_file"

  if [ ! -f "$csv_file" ]; then
    log "ERROR: Plik CSV nie istnieje: $csv_file"
    echo "[]"
    return 1
  fi

  log "Przetwarzanie pliku CSV: $csv_file" >&2

  # Pobieramy unikalne pluginy z CSV (czyścimy z cudzysłowów)
  local unique_plugins=$(tail -n +2 "$csv_file" | cut -d',' -f2 | sed 's/^"//;s/"$//' | sort -u | grep -v "^Plugin$" | grep -v "^$")

  # Przetwarzamy każdy unikalny plugin
  while IFS= read -r plugin; do
    if [ -n "$plugin" ]; then
      # Dodatkowe czyszczenie nazwy pluginu
      plugin=$(echo "$plugin" | sed 's/^"//;s/"$//' | tr -d '\r\n')

      log "Pobieranie najnowszej wersji dla pluginu: $plugin" >&2
      latest_version=$(get_plugin_latest_version "$plugin")
      log "Plugin: $plugin, Najnowsza wersja: $latest_version" >&2

      # Dodajemy informacje o pluginie do pliku tymczasowego
      if [ -n "$latest_version" ]; then
        jq --arg slug "$plugin" --arg latest_version "$latest_version" \
          '. += [{"slug": $slug, "latest_version": $latest_version}]' \
          "$temp_plugins_file" >"${temp_plugins_file}.tmp" &&
          mv "${temp_plugins_file}.tmp" "$temp_plugins_file"
      fi
    fi
  done <<<"$unique_plugins"

  # Teraz przetwarzamy CSV i łączymy z informacjami o pluginach
  local temp_final_file="/tmp/final_plugins_info_$$.json"
  echo "[]" >"$temp_final_file"

  # Przetwarzamy każdą linię CSV
  tail -n +2 "$csv_file" | while IFS=',' read -r url plugin version severity auth_type cves cve_links cvss_score cvss_vector title; do
    if [ -n "$plugin" ] && [ "$plugin" != "Plugin" ]; then
      # Czyścimy dane z potencjalnych cudzysłowów
      plugin=$(echo "$plugin" | sed 's/^"//;s/"$//' | tr -d '\r\n')
      version=$(echo "$version" | sed 's/^"//;s/"$//')
      severity=$(echo "$severity" | sed 's/^"//;s/"$//')
      auth_type=$(echo "$auth_type" | sed 's/^"//;s/"$//')
      cves=$(echo "$cves" | sed 's/^"//;s/"$//')
      cve_links=$(echo "$cve_links" | sed 's/^"//;s/"$//')
      cvss_score=$(echo "$cvss_score" | sed 's/^"//;s/"$//')
      cvss_vector=$(echo "$cvss_vector" | sed 's/^"//;s/"$//')
      title=$(echo "$title" | sed 's/^"//;s/"$//')

      # Pobieramy najnowszą wersję z naszego cache
      latest_version=$(jq -r --arg slug "$plugin" '.[] | select(.slug == $slug) | .latest_version' "$temp_plugins_file")
      if [ -z "$latest_version" ] || [ "$latest_version" = "null" ]; then
        latest_version="unknown"
      fi

      # Dodajemy informacje o pluginie do JSON
      jq --arg slug "$plugin" \
        --arg current_version "$version" \
        --arg latest_version "$latest_version" \
        --arg severity "$severity" \
        --arg auth_type "$auth_type" \
        --arg cves "$cves" \
        --arg cve_links "$cve_links" \
        --arg cvss_score "$cvss_score" \
        --arg cvss_vector "$cvss_vector" \
        --arg title "$title" \
        '. += [{
                   slug: $slug,
                   current_version: $current_version,
                   latest_version: $latest_version,
                   severity: $severity,
                   auth_type: $auth_type,
                   cves: $cves,
                   cve_links: $cve_links,
                   cvss_score: $cvss_score,
                   cvss_vector: $cvss_vector,
                   title: $title
               }]' "$temp_final_file" >"${temp_final_file}.tmp" &&
        mv "${temp_final_file}.tmp" "$temp_final_file"
    fi
  done

  # Odczytujemy wynik z pliku
  local final_plugins_info=$(cat "$temp_final_file")

  # Usuwamy pliki tymczasowe
  rm -f "$temp_plugins_file" "$temp_final_file"

  echo "$final_plugins_info"
}

# Funkcja wysyłania danych na webhook
send_to_webhook() {
  local domain_data="$1"
  local csv_content="$2"
  local plugins_info="$3"

  # Sprawdzamy czy domain_data jest prawidłowym JSON
  if ! echo "$domain_data" | jq . >/dev/null 2>&1; then
    log "ERROR: domain_data nie jest prawidłowym JSON"
    return 1
  fi

  # Sprawdzamy czy plugins_info jest prawidłowym JSON
  if ! echo "$plugins_info" | jq . >/dev/null 2>&1; then
    log "ERROR: plugins_info nie jest prawidłowym JSON"
    return 1
  fi

  # Escapujemy csv_content dla JSON
  local escaped_csv_content=$(echo "$csv_content" | jq -Rs .)

  local payload=$(jq -n \
    --argjson domain_data "$domain_data" \
    --argjson csv_content "$escaped_csv_content" \
    --argjson plugins_info "$plugins_info" \
    --arg timestamp "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)" \
    '{
            timestamp: $timestamp,
            domain_data: $domain_data,
            csv_content: $csv_content,
            plugins_info: $plugins_info
        }')

  if [ $? -ne 0 ]; then
    log "ERROR: Błąd tworzenia JSON payload"
    return 1
  fi

  log "Wysyłanie danych na webhook: $CURRENT_WEBHOOK_URL"

  local response=$(curl -s -w "%{http_code}" -X POST \
    -H "Content-Type: application/json" \
    -d "$payload" \
    "$CURRENT_WEBHOOK_URL")

  local http_code="${response: -3}"
  local response_body="${response%???}"

  if [ "$http_code" -eq 200 ] || [ "$http_code" -eq 201 ]; then
    log "SUCCESS: Dane wysłane pomyślnie (HTTP $http_code)"
  else
    log "ERROR: Błąd wysyłania danych (HTTP $http_code): $response_body"
  fi
}

# Główna funkcja
main() {
  log "Rozpoczynanie skryptu wpprobe audit"

  # Informacja o trybie
  if [ "$TEST_MODE" = true ]; then
    log "Tryb testowy aktywny - używanie TEST_WEEBHOOK_URL: $CURRENT_WEBHOOK_URL"
  else
    log "Tryb produkcyjny - używanie WEBHOOK_URL: $CURRENT_WEBHOOK_URL"
  fi

  # Informacja o parametrach API
  if [ -n "$API_PARAMS" ]; then
    log "Używane parametry API: $API_PARAMS"
  else
    log "Brak dodatkowych parametrów API"
  fi

  log "Końcowy API endpoint: $API_ENDPOINT"

  # Sprawdzenie czy wymagane narzędzia są dostępne
  for tool in curl jq wpprobe lighthouse; do
    if ! command -v "$tool" &>/dev/null; then
      log "ERROR: Wymagane narzędzie '$tool' nie jest zainstalowane"
      exit 1
    fi
  done

  # Tworzenie katalogów jeśli nie istnieją

  # Pobieranie danych z API
  log "Pobieranie danych z API: $API_ENDPOINT"
  api_response=$(curl -s "$API_ENDPOINT")

  if [ $? -ne 0 ] || [ -z "$api_response" ]; then
    log "ERROR: Nie udało się pobrać danych z API"
    exit 1
  fi

  log "Odpowiedź z API: $api_response"

  # Sprawdzenie czy odpowiedź zawiera domeny
  domains_count=$(echo "$api_response" | jq -r '.domains | length')

  if [ "$domains_count" -eq 0 ]; then
    log "INFO: Brak domen do audytu"
    exit 0
  fi

  # Przetwarzanie każdej domeny
  echo "$api_response" | jq -c '.domains[]' | while read -r domain; do
    domain_id=$(echo "$domain" | jq -r '.id')
    full_domain=$(echo "$domain" | jq -r '.fullDomain')
    mkdir -p /home/<USER>/Projects/ai-studio/scan/audits/${domain_id}

    log "Przetwarzanie domeny: $full_domain (ID: $domain_id)"

    # Uruchomienie Lighthouse przed skanowaniem
    if [ "$SKIP_LIGHTHOUSE" = false ]; then
      lighthouse_csv_file="/home/<USER>/Projects/ai-studio/scan/audits/${domain_id}/${domain_id}.csv"
      log "Uruchamianie Lighthouse dla $full_domain"

      if lighthouse "$full_domain" --preset=desktop --output csv --output html --locale pl --output-path "$lighthouse_csv_file" --chrome-flags="--headless"; then
        log "SUCCESS: Lighthouse zakończone pomyślnie dla $full_domain"
      else
        log "ERROR: Lighthouse nie powiodło się dla $full_domain"
      fi
    else
      log "INFO: Pomijanie Lighthouse zgodnie z flagą --skip-lighthouse"
    fi

    # Uruchomienie wpprobe scan
    csv_file="/home/<USER>/Projects/ai-studio/scan/audits/${domain_id}/${domain_id}_wpprove.csv"
    log "Uruchamianie wpprobe scan dla $full_domain"

    if wpprobe scan -u "$full_domain" -o "$csv_file"; then
      log "SUCCESS: Skanowanie zakończone pomyślnie"

      # Sprawdzenie czy plik CSV został utworzony
      if [ -f "$csv_file" ]; then
        log "Plik CSV utworzony: $csv_file"

        # Odczytanie zawartości CSV
        csv_content=$(cat "$csv_file")

        # Przetwarzanie CSV i pobieranie wersji pluginów
        log "Rozpoczynanie przetwarzania CSV i pobierania wersji pluginów"
        plugins_info=$(process_csv_and_get_versions "$csv_file")

        # Sprawdzenie czy plugins_info jest prawidłowym JSON
        if echo "$plugins_info" | jq . >/dev/null 2>&1; then
          log "SUCCESS: plugins_info jest prawidłowym JSON"
          # Wysłanie danych na webhook
          send_to_webhook "$domain" "$csv_content" "$plugins_info"
        else
          log "ERROR: plugins_info nie jest prawidłowym JSON: $plugins_info"
          # Wysyłamy z pustą tablicą pluginów
          send_to_webhook "$domain" "$csv_content" "[]"
        fi

      else
        log "ERROR: Plik CSV nie został utworzony: $csv_file"
      fi
    else
      log "ERROR: Skanowanie nie powiodło się dla $full_domain"
    fi
  done

  log "Skrypt zakończony"
}

# Uruchomienie głównej funkcji
main "$@"
