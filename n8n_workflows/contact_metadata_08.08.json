{"name": "Kontakt + metadata", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 10}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-1728, 640], "id": "37c9e34f-ed01-4d0d-a274-926f3826cab6", "name": "Schedule Trigger"}, {"parameters": {"url": "=http://localhost:3000/api/domains/to-audit?limit=1&status=in_progress&hasContactData=false", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1552, 640], "id": "581e9876-ce24-40d7-bee4-da9f92b3eb89", "name": "HTTP Request"}, {"parameters": {"assignments": {"assignments": [{"id": "e8067571-c08b-43f0-b473-0f62098ca40e", "name": "id", "value": "={{ $json.domains[0].id }}", "type": "string"}, {"id": "dd446a6a-ac04-4af0-acca-f8bc318fb847", "name": "domain", "value": "={{ $json.domains[0].domain }}", "type": "string"}, {"id": "5e3f0670-23e9-4997-978f-28fde293acdf", "name": "fulldomain", "value": "={{ $json.domains[0].fullDomain }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1360, 640], "id": "a6c40043-276c-4f26-852d-6f9243284fb4", "name": "Edit Fields1"}, {"parameters": {"assignments": {"assignments": [{"id": "e8067571-c08b-43f0-b473-0f62098ca40e", "name": "id", "value": "={{ $json.body.domainId }}", "type": "string"}, {"id": "dd446a6a-ac04-4af0-acca-f8bc318fb847", "name": "domain", "value": "={{ $json.body.domain }}", "type": "string"}, {"id": "5e3f0670-23e9-4997-978f-28fde293acdf", "name": "fulldomain", "value": "={{ $json.body.fullDomain }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1360, 496], "id": "5b8daf39-5d65-4da3-b566-d7f2339b2943", "name": "Edit Fields2"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "id"}, {"fieldToAggregate": "domain"}, {"fieldToAggregate": "fulldomain"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-1072, 640], "id": "0dee8fee-a262-4dc8-87cb-4b7d8f980523", "name": "Aggregate"}, {"parameters": {"method": "POST", "url": "https://api.openai.com/v1/responses", "authentication": "genericCredentialType", "genericAuthType": "httpBearerAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"gpt-4.1-mini\",\n  \"input\": [\n    {\n      \"role\": \"user\",\n      \"content\": [\n        {\n          \"type\": \"input_text\",\n          \"text\": \"\\n\\n\"\n        }\n      ]\n    },\n    {\n      \"role\": \"user\",\n      \"content\": [\n        {\n          \"type\": \"input_text\",\n          \"text\": \"znajdz dane kontaktowe na stronie {{ $('Aggregate').item.json.fulldomain[0] }}, informacje bierz tylko z tej domeny, przes<PERSON>j strone glowna, strone kontaktowa i jak trzeba bedzie inne strony aby znalezc dane kontatkowe do firmy lub wlasciciela strony, nie korzystaj z innych zrodel. pola jakie ma miec json to: domainId (zawsze ustawiaj jako: {{ $('Aggregate').item.json.id[0] }}) \\n companyName, contactPerson, email, phone, address, notes (w notes umiesc inne informacje kontaktowe, linki do social media, godziny otwarcia itp), je<PERSON><PERSON> nie ma jaki<PERSON>s pola , wpisz w tym polu N/A\\n\\n Zwróć wiecej obiektow jezeli jest wiecej niz jedna osoba kontaktowa. Nie dodawaj zadnych innych informacji procz jsona\"\n        }\n      ]\n    }\n  ],\n  \"text\": {\n    \"format\": {\n      \"type\": \"text\"\n    }\n  },\n  \"reasoning\": {},\n  \"tools\": [\n    {\n      \"type\": \"web_search_preview\",\n      \"user_location\": {\n        \"type\": \"approximate\",\n        \"country\": \"PL\"\n      },\n      \"search_context_size\": \"medium\"\n    }\n  ],\n  \"temperature\": 1,\n  \"max_output_tokens\": 2048,\n  \"top_p\": 1,\n  \"store\": true\n}", "options": {"response": {"response": {"responseFormat": "json"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-848, 640], "id": "8799c1c1-1a06-4928-a6ac-f45bcb43c60a", "name": "HTTP Request5", "credentials": {"httpBearerAuth": {"id": "ZhOHMRE98KiW2k2p", "name": "Bearer Auth account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "5a940094-a4ba-4f2e-a06e-b4d3011805ae", "name": "response", "value": "={{ $json.output[1].content[0].text }}", "type": "string"}]}, "options": {"ignoreConversionErrors": false}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-624, 640], "id": "90c97702-ad82-45ff-a060-823cf866321f", "name": "<PERSON>"}, {"parameters": {"jsCode": "let rawString = $input.first().json.response;\n\nconst cleanJsonStr = rawString.replace(/```json\\n?/, '').replace(/\\n?```/, '');\n\n// 2. Parsuj string do tablicy obiektów\nconst jsonArray = JSON.parse(cleanJsonStr);\n\n// 3. Możesz teraz używać jsonArray jak zwykłej tablicy JS\nreturn {jsonArray};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-416, 640], "id": "167fee24-b6dd-4827-99ff-************", "name": "Code"}, {"parameters": {"method": "POST", "url": "http://localhost:3000/api/contact-data", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json.jsonArray }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-224, 640], "id": "f26e9b58-296a-4e85-92e5-0ccabe428578", "name": "HTTP Request6"}, {"parameters": {"method": "POST", "url": "http://localhost:3000/api/logs", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"level\": \"info\",\n  \"message\": \"<PERSON>dano dane kontak<PERSON> do {{ $json.contactData[0].companyName }} [{{ $('Aggregate').item.json.fulldomain[0] }}] ({{ $('Aggregate').item.json.id[0] }})\",\n  \"source\": \"N8N AI contact grabber\",\n  \"userId\": \"0\"\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [16, 640], "id": "cde5c869-956c-4464-b62a-ae1203295701", "name": "HTTP Request22", "onError": "continueErrorOutput"}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-1728, 832], "id": "d06a9f02-db79-43fb-aaf1-b0e6f868bda9", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"path": "3dd6c199-240f-4349-bc0e-180f370a4e09", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2.1, "position": [-1728, 496], "id": "edb64d94-8e53-4cd6-a87a-f9c603bda65e", "name": "Webhook", "webhookId": "3dd6c199-240f-4349-bc0e-180f370a4e09"}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Edit Fields2": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "HTTP Request5", "type": "main", "index": 0}]]}, "HTTP Request5": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "HTTP Request6", "type": "main", "index": 0}]]}, "HTTP Request6": {"main": [[{"node": "HTTP Request22", "type": "main", "index": 0}]]}, "When clicking ‘Execute workflow’": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "5452a381-3dc5-4a7e-8b6a-af76c6156ed6", "meta": {"instanceId": "e861430a935d98630ee53dc2369e1316b287f1dc0b715edc485d8bc344659d56"}, "id": "FlxwtrSqcgc6eWFC", "tags": []}