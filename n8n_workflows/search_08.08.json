{"name": "Wyszukiwanie", "nodes": [{"parameters": {"content": "http://localhost:3000/api/search\n\nParametry wejściowe:                                                                                         │\n│  - query (wymagane) - zapytanie wyszukiwania                                                                  │\n│  - category (wymagane) - kate<PERSON><PERSON>                                                                            │\n│  - location (opcjonalnie) - lokaliza<PERSON><PERSON>                                                                       │\n│  - page (opcjonalnie) - numer strony (1-100) ", "height": 704, "width": 2544}, "type": "n8n-nodes-base.stickyNote", "position": [-2752, -832], "typeVersion": 1, "id": "af30b496-807c-465a-826c-4fc4c1844dcb", "name": "<PERSON><PERSON>"}, {"parameters": {"url": "http://localhost:3000/api/public/categories", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-2480, -624], "id": "b4ed076e-8653-474b-9b26-0470ee5b9390", "name": "HTTP Request"}, {"parameters": {"url": "=http://localhost:3000/api/public/searches?category={{ $json.name }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1936, -624], "id": "ebace1f0-022d-4704-b184-01cf9cbd10e2", "name": "HTTP Request1"}, {"parameters": {"promptType": "define", "text": "=Jesteś profesjonalny researcherem. Potrzebuję znaleźć strony w google firm którym może zależeć na stronie internetowej. Zalezy nam na stronach firm, nie portale, strony infromacyjne, fora itp.\n\nKategoria do jakiej wymyślasz dane do zapytania to: {{ $json.category }} \nObecnie w historii są już takie zapytania: {{ JSON.stringify($json.searches) }}\n\nStrony muszą być w języku polskim, powinny należeć do firmy lub osoby prywatnej (omiń placówki rządowe, fundacje itp). . Sprzedaz mieszkan, domow i tak dalej.\n\nzwróć dane do zapytania w json których nie było w historii i mają duże szanse aby się nie powtórzyć\n1 \"query\" - zapytanie\n2 \"location\" - lokalizacja z jakiej ma być wyszukiwanie - może być to miasto, region w polsce\n3 \"page\" - strona w google z której będą brane wyniki dla danej frazy (od 1 do 100)\n\n", "hasOutputParser": true, "options": {"systemMessage": "=Zwr<PERSON><PERSON> dane w takim formacie\n{\n\"query\": \"\",\n\"category\": \"\",\n\"location\": \"\",\n\"page\": \"\",\n\"userId\": \"\"\n} "}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.2, "position": [-1616, -624], "id": "e838173e-68df-4ffb-9d84-d37284f96a9d", "name": "AI Agent"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [-1616, -384], "id": "4f9889e9-9e74-458d-a011-bcfb85f0d02d", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "A8ESJfNt1YU7cqKI", "name": "OpenRouter account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "eaa6b1e3-3821-4329-99ea-aa7687447a58", "name": "output", "value": "={{ $json.output }}", "type": "object"}, {"id": "199004d1-a62f-40c5-aad5-6691d9311a4e", "name": "output.userId", "value": "=6837ee9cec5d1d48ebb98e51", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1280, -624], "id": "0d199470-93f3-420f-b030-a323486408ca", "name": "Edit Fields1"}, {"parameters": {"method": "POST", "url": "http://localhost:3000/api/search", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json.output }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-880, -624], "id": "8c753250-7a54-4c0e-ad82-15507374f60b", "name": "HTTP Request2", "onError": "continueErrorOutput"}, {"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 1}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-2704, -624], "id": "613146b9-3ee1-4456-8a8e-3cf5b9edf91c", "name": "Schedule Trigger"}, {"parameters": {"jsCode": "// <PERSON> z obiektu\nconst data = [\n  {\n    \"success\": true,\n    \"categories\": [\n      {\n        \"id\": \"68935e9e3ab47198d6689b64\",\n        \"name\": \"<PERSON><PERSON><PERSON><PERSON>, twó<PERSON>y i tatuaż<PERSON>ści\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-08-06T13:54:38.972Z\",\n        \"updatedAt\": \"2025-08-06T13:54:38.972Z\"\n      },\n      {\n        \"id\": \"6843c21d080ca01574535f88\",\n        \"name\": \"Budownictwo\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-06-07T04:37:49.702Z\",\n        \"updatedAt\": \"2025-08-06T13:54:47.351Z\"\n      },\n      {\n        \"id\": \"68803becc1ed1d4f4dcf141f\",\n        \"name\": \"Catering i restauracje\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-07-23T01:33:32.808Z\",\n        \"updatedAt\": \"2025-08-06T13:54:57.786Z\"\n      },\n      {\n        \"id\": \"68935eba3ab47198d6689b6d\",\n        \"name\": \"Coaching i mentoring\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-08-06T13:55:06.766Z\",\n        \"updatedAt\": \"2025-08-06T13:55:06.766Z\"\n      },\n      {\n        \"id\": \"68935ec13ab47198d6689b70\",\n        \"name\": \"Doradztwo biznesowe\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-08-06T13:55:13.166Z\",\n        \"updatedAt\": \"2025-08-06T13:55:13.166Z\"\n      },\n      {\n        \"id\": \"68935ee43ab47198d6689b73\",\n        \"name\": \"Edukacja\",\n        \"description\": \"(korepetytorzy, kursy online)\",\n        \"createdAt\": \"2025-08-06T13:55:48.766Z\",\n        \"updatedAt\": \"2025-08-06T13:55:48.766Z\"\n      },\n      {\n        \"id\": \"68935ef03ab47198d6689b76\",\n        \"name\": \"Eventy i organizacja imprez\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-08-06T13:56:00.419Z\",\n        \"updatedAt\": \"2025-08-06T13:56:00.419Z\"\n      },\n      {\n        \"id\": \"68935efa3ab47198d6689b79\",\n        \"name\": \"Finanse i bankowość\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-08-06T13:56:10.433Z\",\n        \"updatedAt\": \"2025-08-06T13:56:10.433Z\"\n      },\n      {\n        \"id\": \"68935efe3ab47198d6689b7c\",\n        \"name\": \"Fotografia\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-08-06T13:56:14.269Z\",\n        \"updatedAt\": \"2025-08-06T13:56:14.269Z\"\n      },\n      {\n        \"id\": \"685f9e9c74d6a3e8cf7b690b\",\n        \"name\": \"Fotowoltaika\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-06-28T07:49:48.343Z\",\n        \"updatedAt\": \"2025-06-28T07:49:48.343Z\"\n      },\n      {\n        \"id\": \"68935f083ab47198d6689b80\",\n        \"name\": \"Fryzjerstwo i salony urody\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-08-06T13:56:24.725Z\",\n        \"updatedAt\": \"2025-08-06T13:56:24.725Z\"\n      },\n\n      {\n        \"id\": \"68935f133ab47198d6689b86\",\n        \"name\": \"Grafika i projektowanie stron\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-08-06T13:56:35.662Z\",\n        \"updatedAt\": \"2025-08-06T13:56:35.662Z\"\n      },\n      {\n        \"id\": \"68935f183ab47198d6689b89\",\n        \"name\": \"Hotele i pensjonaty\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-08-06T13:56:40.931Z\",\n        \"updatedAt\": \"2025-08-06T13:56:40.931Z\"\n      },\n\n      {\n        \"id\": \"68935f373ab47198d6689b95\",\n        \"name\": \"Jubilerstwo\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-08-06T13:57:11.534Z\",\n        \"updatedAt\": \"2025-08-06T13:57:11.534Z\"\n      },\n      {\n        \"id\": \"6851bb85ec2af31c4bb62803\",\n        \"name\": \"Księgowość\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-06-17T19:01:25.230Z\",\n        \"updatedAt\": \"2025-06-17T19:01:25.230Z\"\n      },\n      {\n        \"id\": \"68935f3d3ab47198d6689b98\",\n        \"name\": \"Logistyka i transport\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-08-06T13:57:17.261Z\",\n        \"updatedAt\": \"2025-08-06T13:57:17.261Z\"\n      },\n      {\n        \"id\": \"68935f423ab47198d6689b9b\",\n        \"name\": \"Marketing i reklama\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-08-06T13:57:22.975Z\",\n        \"updatedAt\": \"2025-08-06T13:57:22.975Z\"\n      },\n      {\n        \"id\": \"6880c867e2e11b2a64cf43c4\",\n        \"name\": \"Med-beauty\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-07-23T11:32:55.288Z\",\n        \"updatedAt\": \"2025-07-23T11:32:55.288Z\"\n      },\n\n\n      {\n        \"id\": \"68935f683ab47198d6689ba3\",\n        \"name\": \"Nieruchomości\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-08-06T13:58:00.464Z\",\n        \"updatedAt\": \"2025-08-06T13:58:00.464Z\"\n      },\n\n      {\n        \"id\": \"68935f723ab47198d6689ba6\",\n        \"name\": \"Odzież\",\n        \"description\": \"(sklepy, projektanci)\",\n        \"createdAt\": \"2025-08-06T13:58:10.188Z\",\n        \"updatedAt\": \"2025-08-06T13:58:10.188Z\"\n      },\n      {\n        \"id\": \"68935f843ab47198d6689bac\",\n        \"name\": \"Organizacja ślubów i wesel\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-08-06T13:58:28.344Z\",\n        \"updatedAt\": \"2025-08-06T13:58:28.344Z\"\n      },\n\n      {\n        \"id\": \"68935f983ab47198d6689bb2\",\n        \"name\": \"Projektowanie ogrodów\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-08-06T13:58:48.247Z\",\n        \"updatedAt\": \"2025-08-06T13:58:48.247Z\"\n      },\n      {\n        \"id\": \"68935f9e3ab47198d6689bb5\",\n        \"name\": \"Psychologia i psychoterapia\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-08-06T13:58:54.697Z\",\n        \"updatedAt\": \"2025-08-06T13:58:54.697Z\"\n      },\n      {\n        \"id\": \"68935fa43ab47198d6689bb8\",\n        \"name\": \"Rekrutacja\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-08-06T13:59:00.686Z\",\n        \"updatedAt\": \"2025-08-06T13:59:00.686Z\"\n      },\n      {\n        \"id\": \"68935faa3ab47198d6689bbb\",\n        \"name\": \"Salony samochodowe\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-08-06T13:59:06.988Z\",\n        \"updatedAt\": \"2025-08-06T13:59:06.988Z\"\n      },\n      {\n        \"id\": \"68935fba3ab47198d6689bc1\",\n        \"name\": \"Sport\",\n        \"description\": \"(kluby, instruktorzy)\",\n        \"createdAt\": \"2025-08-06T13:59:22.748Z\",\n        \"updatedAt\": \"2025-08-06T13:59:22.748Z\"\n      },\n      {\n        \"id\": \"686f612d967a09cef99aeb52\",\n        \"name\": \"Technologia\",\n        \"description\": \"Najnowsze trendy i nowości technologiczne\",\n        \"createdAt\": \"2025-07-10T06:43:57.583Z\",\n        \"updatedAt\": \"2025-07-10T06:43:57.583Z\"\n      },\n      {\n        \"id\": \"687df5f051747ea4890b9660\",\n        \"name\": \"Trenerzy\",\n        \"description\": \"Trenerzy personalni, sportowi itp\",\n        \"createdAt\": \"2025-07-21T08:10:24.864Z\",\n        \"updatedAt\": \"2025-07-21T08:10:24.864Z\"\n      },\n      {\n        \"id\": \"68935fc03ab47198d6689bc4\",\n        \"name\": \"Ubezpieczenia\",\n        \"description\": \"\",\n        \"createdAt\": \"2025-08-06T13:59:28.545Z\",\n        \"updatedAt\": \"2025-08-06T13:59:28.545Z\"\n      },\n      {\n        \"id\": \"68935fd43ab47198d6689bc7\",\n        \"name\": \"Wynajem\",\n        \"description\": \"(samochodów, sprzętu)\",\n        \"createdAt\": \"2025-08-06T13:59:48.151Z\",\n        \"updatedAt\": \"2025-08-06T13:59:48.151Z\"\n      },\n    ]\n  }\n];\n\n// Przypisanie tablicy kategorii do zmiennej\nconst categories = data[0].categories;\n\n// Funkcja do losowania jednej kategorii\nfunction getRandomCategory() {\n  const randomIndex = Math.floor(Math.random() * categories.length);\n  return categories[randomIndex];\n}\n\n// Wylosowanie kategorii\nconst randomCategory = getRandomCategory();\n\n// Wyświetlenie wyniku\nconsole.log(\"Wylosowana kategoria:\");\nconsole.log(`Nazwa: ${randomCategory.name}`);\nconsole.log(`ID: ${randomCategory.id}`);\nif (randomCategory.description) {\n  console.log(`Opis: ${randomCategory.description}`);\n}\n\n// Alternatywnie - krótsza wersja w jednej linii\nconst randomCategoryOneLiner = categories[Math.floor(Math.random() * categories.length)];\nreturn {\n  \"name\" : randomCategoryOneLiner.name\n  //\"name\" : \"Developer\"\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2272, -624], "id": "d62e9de0-c105-4b4f-b0ba-48f1d9487ba2", "name": "Code"}, {"parameters": {"method": "POST", "url": "http://localhost:3000/api/logs", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"level\": \"info\",\n  \"message\": \"Ukończono wyszukiwanie dla [{{ $json.searchQuery.query }}] (+{{ $json.domainsCount }} domen)\",\n  \"source\": \"N8N Search\",\n  \"userId\": \"0\"\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-528, -640], "id": "4176876f-6116-43f9-b658-8b2b3c8f32fb", "name": "HTTP Request20", "onError": "continueErrorOutput"}, {"parameters": {"assignments": {"assignments": [{"id": "eaa6b1e3-3821-4329-99ea-aa7687447a58", "name": "output", "value": "={{ $json.output }}", "type": "object"}, {"id": "199004d1-a62f-40c5-aad5-6691d9311a4e", "name": "output.userId", "value": "=6837ee9cec5d1d48ebb98e51", "type": "string"}, {"id": "97f8e3ed-fe60-4f56-8d81-e2fb12b01507", "name": "output.location", "value": "={{ $json.output.location }}, Polska", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1072, -624], "id": "eba8ffb6-be13-49bc-aacf-ebd9646b032a", "name": "<PERSON>"}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-2704, -432], "id": "350fc9b1-6d3f-458c-a24f-6b5f69b45c7b", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"method": "POST", "url": "http://localhost:3000/api/logs", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"level\": \"error\",\n  \"message\": \"Nieukończono wyszukiwanie dla [{{ $json.searchQuery.query }}] (+{{ $json.domainsCount }} domen)\",\n  \"source\": \"N8N Search\",\n  \"userId\": \"0\"\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-736, -448], "id": "fb294001-edac-4675-987b-c04888325497", "name": "HTTP Request21", "onError": "continueErrorOutput"}, {"parameters": {"errorMessage": "Nie ukończono wyszukiwania"}, "type": "n8n-nodes-base.stopAndError", "typeVersion": 1, "position": [-480, -464], "id": "15df32e7-d43f-4b17-9bf2-f8106129142a", "name": "Stop and Error"}], "pinData": {}, "connections": {"HTTP Request": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "HTTP Request2": {"main": [[{"node": "HTTP Request20", "type": "main", "index": 0}], [{"node": "HTTP Request21", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "HTTP Request2", "type": "main", "index": 0}]]}, "When clicking ‘Execute workflow’": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request21": {"main": [[{"node": "Stop and Error", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "8e8180ca-f2b6-469d-a989-29d149dfcc89", "meta": {"templateCredsSetupCompleted": true, "instanceId": "e861430a935d98630ee53dc2369e1316b287f1dc0b715edc485d8bc344659d56"}, "id": "jvse5RBkZKOQY0U9", "tags": []}