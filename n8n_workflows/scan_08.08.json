{"name": "<PERSON><PERSON>", "nodes": [{"parameters": {"httpMethod": "POST", "path": "df465d08-3a18-4d4f-afd6-65d14d8c6265", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2.1, "position": [-1424, -160], "id": "cc903a1e-ee64-48d6-b70a-0d308494dcec", "name": "Webhook", "webhookId": "df465d08-3a18-4d4f-afd6-65d14d8c6265"}, {"parameters": {"command": "=wpscan --url {{ $('Aggregate').item.json.fulldomain[0] }} --update --output /home/<USER>/Projects/ai-studio/scan/scans/{{ $('Aggregate').item.json.id[0] }}.json --format json -e ap,u  --random-user-agent --ignore-main-redirect \n"}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-224, 0], "id": "2adc4091-23a1-40fe-9e61-ba4842794b13", "name": "Execute Command2", "onError": "continueErrorOutput"}, {"parameters": {"url": "=http://localhost:3000/api/domains/get-domain?id={{ $json.id[0] }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-368, 0], "id": "a5a0808a-a530-49d9-a8d4-3533112a922d", "name": "HTTP Request1"}, {"parameters": {"command": "=cd /home/<USER>/Projects/ai-studio/scan/\npython3 main.py --id {{ $('Aggregate').item.json.id[0] }}"}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-16, -240], "id": "5677f278-d16b-4484-8dc4-0d2a384e9923", "name": "Execute Command", "onError": "continueErrorOutput"}, {"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 1}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-1440, 0], "id": "a48812f3-af3d-4698-a900-698baed49386", "name": "Schedule Trigger"}, {"parameters": {"url": "=http://localhost:3000/api/domains/get-domain?status=new&limit=1", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1248, 0], "id": "d9e9ef1c-a3e0-4180-9aab-4d84180398bf", "name": "HTTP Request"}, {"parameters": {"assignments": {"assignments": [{"id": "e8067571-c08b-43f0-b473-0f62098ca40e", "name": "id", "value": "={{ $json.domains[0].id }}", "type": "string"}, {"id": "dd446a6a-ac04-4af0-acca-f8bc318fb847", "name": "domain", "value": "={{ $json.domains[0].domain }}", "type": "string"}, {"id": "5e3f0670-23e9-4997-978f-28fde293acdf", "name": "fulldomain", "value": "={{ $json.domains[0].fullDomain }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1056, 0], "id": "dd79e4a5-1cbd-46a2-8a09-f078ffb300c7", "name": "Edit Fields1"}, {"parameters": {"assignments": {"assignments": [{"id": "e8067571-c08b-43f0-b473-0f62098ca40e", "name": "id", "value": "={{ $json.body.domainId }}", "type": "string"}, {"id": "dd446a6a-ac04-4af0-acca-f8bc318fb847", "name": "domain", "value": "={{ $json.body.domain }}", "type": "string"}, {"id": "5e3f0670-23e9-4997-978f-28fde293acdf", "name": "fulldomain", "value": "={{ $json.body.fullDomain }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1056, -160], "id": "18eb683c-3948-44f6-98a3-8fd2ac5b44d4", "name": "Edit Fields2"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "id"}, {"fieldToAggregate": "domain"}, {"fieldToAggregate": "fulldomain"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-768, 0], "id": "9cbbf0e0-a4c0-4c18-beee-e35e926fa6b0", "name": "Aggregate"}, {"parameters": {"method": "PATCH", "url": "http://localhost:3000/api/webhooks/next-accepted-domain/", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n\"id\": \"{{ $('Aggregate').item.json.id[0] }}\",\n\"status\": \"hold\",\n\"contact_data\": {\n\"contact_notes\": \"\"\n},\n\"metadata\": {\n\"hasScan\": \"false\"\n}\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [400, 16], "id": "ac7455ad-6479-4c64-bae7-7227ef24264b", "name": "HTTP Request3", "onError": "continueErrorOutput"}, {"parameters": {}, "type": "n8n-nodes-base.errorTrigger", "typeVersion": 1, "position": [-1440, 416], "id": "2fb3e145-35f8-433c-8a4a-ec6a88c0ed74", "name": "<PERSON><PERSON><PERSON>"}, {"parameters": {"method": "POST", "url": "http://localhost:3000/api/logs", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"level\": \"error\",\n  \"message\": \"{{ $json.domain.fullDomain }} | {{ $('Extract from File1').item.json.data.scan_aborted }}\",\n  \"source\": \"{{ $json.domain.id }}\",\n  \"userId\": \"0\"\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [640, 0], "id": "ff93573a-63c5-4c94-90f2-54712c6ea84a", "name": "HTTP Request19", "onError": "continueErrorOutput"}, {"parameters": {"errorMessage": "={{ $('HTTP Request3').item.json.domain.contact_data.contact_notes }}"}, "type": "n8n-nodes-base.stopAndError", "typeVersion": 1, "position": [880, -16], "id": "ea9e5c3b-7662-431f-a344-7fe268060d21", "name": "Stop and Error1"}, {"parameters": {"fileSelector": "=/home/<USER>/Projects/ai-studio/scan/scans/{{ $('Aggregate').item.json.id[0] }}.json", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [176, -224], "id": "b241a8fd-8ba1-4d0a-aa1a-5fca23d02c00", "name": "Read/Write Files from Disk2"}, {"parameters": {"operation": "fromJson", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [336, -224], "id": "b62c34d7-16e6-42e6-a0b3-8bfd978baa9c", "name": "Extract from File2"}, {"parameters": {"method": "PATCH", "url": "http://localhost:3000/api/webhooks/next-accepted-domain/", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n\"id\": \"{{ $('Aggregate').item.json.id[0] }}\",\n\"status\": \"hold\",\n\"contact_data\": {\n\"contact_notes\": \"{{ $json.data.scan_aborted }}\"\n},\n\"metadata\": {\n\"hasScan\": \"false\"\n}\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [528, -224], "id": "3b8a9b2c-7929-42a8-8e30-89192470a042", "name": "HTTP Request4", "onError": "continueErrorOutput"}, {"parameters": {"method": "POST", "url": "http://localhost:3000/api/logs", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"level\": \"error\",\n  \"message\": \"{{ $('Extract from File1').item.json.data.scan_aborted }}\",\n  \"source\": \"{{ $('Aggregate').item.json.domain[0] }},\",\n  \"userId\": \"0\"\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [784, -240], "id": "a0be4520-fc17-4ea1-bdaf-781f9c6e457a", "name": "HTTP Request21", "onError": "continueErrorOutput"}, {"parameters": {"errorMessage": "={{ $('HTTP Request4').item.json.domain.contact_data.contact_notes }}"}, "type": "n8n-nodes-base.stopAndError", "typeVersion": 1, "position": [992, -256], "id": "ca9baff3-c67e-432d-958b-f45930c3e95e", "name": "Stop and Error"}, {"parameters": {"method": "POST", "url": "http://localhost:3000/api/logs", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"level\": \"info\",\n  \"message\": \"Wykonano skan dla domeny {{ $json.domain.domain }}\",\n  \"source\": \"N8N AI scan\",\n  \"userId\": \"0\"\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [176, -416], "id": "d662c43c-9462-4918-9be8-34fd2efa9dce", "name": "HTTP Request20", "onError": "continueErrorOutput"}, {"parameters": {"operation": "fromJson", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [192, 16], "id": "2306c2f9-a125-47f0-9d72-2af787643b58", "name": "Extract from File1"}, {"parameters": {"fileSelector": "=/home/<USER>/Projects/ai-studio/scan/scans/{{ $('Aggregate').item.json.id[0] }}.json", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [16, 16], "id": "27ce10c5-9780-4ade-a8b2-97cb87f4d8e5", "name": "Read/Write Files from Disk1"}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-1440, 176], "id": "39e5d84b-a9a7-4354-b3ef-072077f12855", "name": "When clicking ‘Execute workflow’"}], "pinData": {"Webhook": [{"json": {"headers": {"host": "localhost:5678", "connection": "keep-alive", "content-type": "application/json", "accept": "*/*", "accept-language": "*", "sec-fetch-mode": "cors", "user-agent": "node", "accept-encoding": "gzip, deflate", "content-length": "254"}, "params": {}, "query": {}, "body": {"domainId": "68956cec8fc727819cbf48f6", "domain": "buczynski-tailoring.pl", "fullDomain": "https://buczynski-tailoring.pl", "cms": "wordpress", "protocol": "https", "category": "<PERSON><PERSON><PERSON><PERSON>", "userId": "6837ee9cec5d1d48ebb98e51", "requestedAt": "2025-08-08T03:31:21.149Z"}, "webhookUrl": "http://0.0.0.0:5678/webhook-test/df465d08-3a18-4d4f-afd6-65d14d8c6265", "executionMode": "test"}}]}, "connections": {"Webhook": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Execute Command2", "type": "main", "index": 0}]]}, "Execute Command2": {"main": [[{"node": "Execute Command", "type": "main", "index": 0}], [{"node": "Read/Write Files from Disk1", "type": "main", "index": 0}]]}, "Execute Command": {"main": [[{"node": "HTTP Request20", "type": "main", "index": 0}], [{"node": "Read/Write Files from Disk2", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Edit Fields2": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "HTTP Request3": {"main": [[{"node": "HTTP Request19", "type": "main", "index": 0}]]}, "Error Trigger": {"main": [[]]}, "HTTP Request19": {"main": [[{"node": "Stop and Error1", "type": "main", "index": 0}]]}, "Read/Write Files from Disk2": {"main": [[{"node": "Extract from File2", "type": "main", "index": 0}]]}, "HTTP Request4": {"main": [[{"node": "HTTP Request21", "type": "main", "index": 0}]]}, "HTTP Request21": {"main": [[{"node": "Stop and Error", "type": "main", "index": 0}]]}, "Extract from File2": {"main": [[{"node": "HTTP Request4", "type": "main", "index": 0}]]}, "Extract from File1": {"main": [[{"node": "HTTP Request3", "type": "main", "index": 0}]]}, "Read/Write Files from Disk1": {"main": [[{"node": "Extract from File1", "type": "main", "index": 0}]]}, "When clicking ‘Execute workflow’": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "f5ce7b50-e815-4539-abe6-a27e1f530d9e", "meta": {"templateCredsSetupCompleted": true, "instanceId": "e861430a935d98630ee53dc2369e1316b287f1dc0b715edc485d8bc344659d56"}, "id": "OAP6BvQeVhJdQCZL", "tags": []}