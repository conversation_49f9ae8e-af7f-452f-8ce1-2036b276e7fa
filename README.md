# AI Studio

AI Studio to nowoczesna platforma do pracy z sztuczną inteligencją, zbudowana w Next.js z wykorzystaniem MongoDB jako bazy danych.

## Funkcjonalności

- 🔐 **System autoryzacji** - Rejestracja i logowanie użytkowników
- 🎨 **Ciemny motyw** - Wykorzystanie shadcn/ui z ciemnym motywem
- 🛡️ **Zabezpieczony dashboard** - Chroniony dostęp do panelu użytkownika
- 📱 **Responsywny design** - Działa na wszystkich urządzeniach
- 🗄️ **MongoDB** - Nowoczesna baza danych NoSQL
- 🔍 **Agregator wyszukiwania** - Integracja z Serper API do wyszukiwania Google z obsługą lokalizacji
- 🔗 **Zarządzanie linkami** - Automatyczne zapisywanie i kategoryzowanie linków z informacją o lokalizacji
- 🌐 **Analiza domen** - Agregacja i analiza domen z wyszukiwań
- ✏️ **Edycja kategorii domen** - Możliwość zmiany kategorii domeny w szczegółach
- 🗑️ **Usuwanie domen** - Możliwość usunięcia domeny wraz z powiązanymi linkami z widoku szczegółów
- 📧 **Historia maili audytowych** - Automatyczne zapisywanie i śledzenie wysłanych maili
- 📊 **Śledzenie maili** - Monitoring otwarć, kliknięć i dostarczeń maili audytowych

## Technologie

- **Frontend**: Next.js 15.3.3, React 19, TypeScript 5
- **UI**: shadcn/ui, Tailwind CSS 4.0, Lucide React 0.511.0
- **Autoryzacja**: NextAuth.js 4.24.11
- **Baza danych**: MongoDB z Mongoose 8.15.1
- **Walidacja**: Zod 3.25.42, React Hook Form 7.56.4
- **Notyfikacje**: Sonner 2.0.3
- **Wykresy**: Recharts 2.15.3
- **Motywy**: Next Themes 0.4.6
- **Markdown**: React Markdown 10.1.0, Remark GFM 4.0.1, Tailwind Typography 0.5.16
- **Mail Tracking**: nodemailer-mail-tracking 0.7.0, jsonwebtoken

## Struktura bazy danych

### Kolekcja: users

```javascript
{
  _id: ObjectId,
  email: String (unique, required),
  password: String (hashed, required),
  name: String (required),
  createdAt: Date,
  updatedAt: Date
}
```

**Indeksy:**

- `email: 1` - dla szybszego wyszukiwania użytkowników

### Kolekcja: searchqueries

```javascript
{
  _id: ObjectId,
  query: String (required, max 500 chars),
  category: String (required, max 100 chars),
  location: String (optional, max 200 chars),
  page: Number (required, min 1, default 1),
  userId: String (required),
  createdAt: Date,
  updatedAt: Date
}
```

**Indeksy:**

- `{ userId: 1, query: 1, category: 1, location: 1 }` - dla szybszego wyszukiwania zapytań użytkownika
- `{ query: 1, category: 1 }` - dla wyszukiwania po query i kategorii
- `{ location: 1 }` - dla wyszukiwania po lokalizacji

### Kolekcja: links

```javascript
{
  _id: ObjectId,
  url: String (required, max 2000 chars),
  title: String (optional, max 500 chars),
  snippet: String (optional, max 1000 chars),
  category: String (required, max 100 chars),
  query: String (required, max 500 chars),
  location: String (optional, max 200 chars),
  userId: String (required),
  searchQueryId: ObjectId (ref: SearchQuery, required),
  createdAt: Date,
  updatedAt: Date
}
```

**Indeksy:**

- `{ userId: 1, category: 1 }` - dla szybszego wyszukiwania linków użytkownika
- `url: 1` - dla wyszukiwania po URL
- `searchQueryId: 1` - dla wyszukiwania po zapytaniu
- `location: 1` - dla wyszukiwania po lokalizacji

### Kolekcja: domains

```javascript
{
  _id: ObjectId,
  domain: String (required, max 255 chars, lowercase),
  protocol: String (required, enum: ['http', 'https'], default: 'https'),
  fullDomain: String (required, lowercase), // protocol + domain
  category: String (required, max 100 chars),
  userId: String (required),
  linkCount: Number (default 1, min 1),
  status: String (enum: ['new', 'accepted', 'rejected', 'closed', 'in_progress', 'in_audit', 'production', 'hold'], default: 'new'), // status domeny
  cms: String (enum: ['wordpress', 'inny']), // typ CMS wykryty automatycznie
  ocena: String (enum: ['wysoka', 'niska', 'brak']), // ocena domeny
  auditContent: String (optional), // treść audytu domeny
  lighthouseContent: String (optional), // wyniki audytu Lighthouse
  contact_data: {
    contact_companyName: String,
    contact_contactPerson: String,
    contact_email: String,
    contact_phone: String,
    contact_address: String,
    contact_notes: String,
    contact_lastUpdated: Date
  },
  metadata: Schema.Types.Mixed, // pole mix na dowolne informacje o domenie
  createdAt: Date,
  updatedAt: Date
}
```

**Indeksy:**

- `{ userId: 1, fullDomain: 1, category: 1 }` - unikalność domeny w kategorii dla użytkownika
- `{ userId: 1, category: 1 }` - dla szybszego wyszukiwania domen użytkownika
- `domain: 1` - dla wyszukiwania po domenie
- `createdAt: 1` - dla sortowania po dacie utworzenia
- `status: 1` - dla filtrowania po statusie domeny

### Kolekcja: categories

```javascript
{
  _id: ObjectId,
  name: String (required, max 100 chars),
  description: String (optional, max 500 chars),
  userId: String (required),
  createdAt: Date,
  updatedAt: Date
}
```

### Kolekcja: logs

```javascript
{
  _id: ObjectId,
  level: String (required, enum: ['info', 'warn', 'error', 'debug']),
  message: String (required, max 1000 chars),
  source: String (optional, max 100 chars),
  userId: String (optional),
  metadata: Object (optional, mix type),
  createdAt: Date,
  updatedAt: Date
}
```

**Indeksy:**

- `{ userId: 1, name: 1 }` - unikalność nazwy kategorii dla użytkownika
- `{ userId: 1 }` - dla szybszego wyszukiwania kategorii użytkownika

### Kolekcja: usersettings

```javascript
{
  _id: ObjectId,
  userId: String (required),
  auditDomainsLimit: Number (required, min: 1, max: 100, default: 1), // liczba domen zwracanych w get-domain
  createdAt: Date,
  updatedAt: Date
}
```

**Indeksy:**

- `{ userId: 1 }` - unikalny indeks dla szybkiego wyszukiwania ustawień użytkownika

### Kolekcja: searchstatistics

```javascript
{
  _id: ObjectId,
  userId: String (required),
  query: String (required), // zapytanie wyszukiwania
  category: String (required), // kategoria wyszukiwania
  page: Number (required), // numer strony
  totalProcessed: Number (required), // liczba przetworzonych wyników
  linksAdded: Number (required), // liczba dodanych linków
  linksSkipped: Number (required), // liczba pominiętych linków
  rejectionRate: Number (required), // wskaźnik odrzucenia (0-1)
  createdAt: Date,
  updatedAt: Date
}
```

**Indeksy:**

- `{ userId: 1, createdAt: -1 }` - dla sortowania po dacie
- `{ userId: 1, query: 1, category: 1 }` - dla grupowania statystyk

### Kolekcja: emailhistories

```javascript
{
  _id: ObjectId,
  domainId: String (required, ref: 'Domain'),
  recipientEmail: String (required, max 255 chars, lowercase),
  subject: String (required, max 500 chars),
  emailType: String (enum: ['audit', 'notification', 'other'], default: 'audit'),
  status: String (enum: ['sent', 'failed', 'pending'], default: 'pending'),
  sentAt: Date (required, default: Date.now),
  userId: String (required),
  emailContent: String (optional, max 50000 chars), // Pełna treść HTML wysłanego maila
  metadata: {
    messageId: String,
    errorMessage: String,
    auditContentLength: Number,
    [key: string]: any
  },
  createdAt: Date,
  updatedAt: Date
}
```

**Indeksy:**

- `{ domainId: 1, sentAt: -1 }` - dla wyszukiwania historii domeny
- `{ userId: 1, sentAt: -1 }` - dla wyszukiwania historii użytkownika
- `{ status: 1, sentAt: -1 }` - dla filtrowania po statusie
- `{ emailType: 1, sentAt: -1 }` - dla filtrowania po typie
- `{ recipientEmail: 1, sentAt: -1 }` - dla wyszukiwania po odbiorcy

### Kolekcja: emailtrackings

```javascript
{
  _id: ObjectId,
  emailHistoryId: String (required, ref: 'EmailHistory'),
  domainId: String (required, ref: 'Domain'),
  recipientEmail: String (required, max 255 chars, lowercase),
  eventType: String (enum: ['sent', 'delivered', 'opened', 'clicked', 'bounced', 'complained'], required),
  eventData: {
    link: String, // dla eventów 'clicked'
    userAgent: String,
    ipAddress: String,
    timestamp: Date (default: Date.now),
    source: String,
    [key: string]: any
  },
  createdAt: Date,
  updatedAt: Date
}
```

**Indeksy:**

- `{ emailHistoryId: 1, eventType: 1 }` - dla wyszukiwania eventów dla konkretnego maila
- `{ domainId: 1, eventType: 1, createdAt: -1 }` - dla statystyk domeny
- `{ recipientEmail: 1, eventType: 1, createdAt: -1 }` - dla statystyk odbiorcy
- `{ eventType: 1, createdAt: -1 }` - dla ogólnych statystyk

## Instalacja i uruchomienie

1. **Sklonuj repozytorium**

```bash
git clone <repository-url>
cd ai-studio
```

2. **Zainstaluj zależności**

```bash
npm install
```

3. **Skonfiguruj zmienne środowiskowe**

```bash
cp .env.example .env.local
```

Edytuj plik `.env.local` i ustaw:

- `MONGODB_URI` - connection string do MongoDB
- `NEXTAUTH_SECRET` - sekretny klucz dla NextAuth.js
- `NEXTAUTH_URL` - URL aplikacji (http://localhost:3000 dla developmentu)
- `SERPER_API_KEY` - klucz API do Serper.dev dla wyszukiwania Google

4. **Uruchom MongoDB**
   Upewnij się, że MongoDB jest uruchomione lokalnie lub skonfiguruj połączenie z MongoDB Atlas.

5. **Uruchom serwer deweloperski**

```bash
npm run dev
```

Otwórz [http://localhost:3000](http://localhost:3000) w przeglądarce.

## Struktura projektu

```
src/
├── app/
│   ├── (auth)/          # Grupa tras autoryzacyjnych
│   │   ├── login/       # Strona logowania
│   │   └── register/    # Strona rejestracji
│   ├── dashboard/       # Chroniony dashboard
│   │   ├── domains/     # Przeglądanie wszystkich domen
│   │   │   ├── page.tsx # Lista domen z filtrami
│   │   │   ├── [id]/    # Szczegóły pojedynczej domeny


│   │   ├── agregator/   # Moduł agregatora wyszukiwania
│   │   │   ├── page.tsx # Główna strona agregatora
│   │   │   └── links/   # Przeglądanie zapisanych linków
│   │   ├── email-tracking/ # Śledzenie maili
│   │   │   └── page.tsx # Dashboard śledzenia maili
│   │   ├── ustawienia/  # Zarządzanie kategoriami
│   │   ├── helpers/     # Narzędzia pomocnicze (tymczasowe)
│   │   └── page.tsx     # Główna strona dashboard
│   ├── api/
│   │   ├── auth/        # NextAuth.js API routes
│   │   ├── register/    # API rejestracji
│   │   ├── search/      # API wyszukiwania Serper
│   │   ├── links/       # API zarządzania linkami
│   │   ├── domains/     # API zarządzania domenami
│   │   │   ├── manual/  # API ręcznego dodawania domen

│   │   │   ├── create-from-links/ # API tworzenia domen z istniejących linków
│   │   │   ├── check-cms/ # API sprawdzania CMS domen
│   │   │   │   └── count/ # API liczenia domen bez CMS
│   │   │   ├── check-duplicates/ # API sprawdzania i usuwania duplikatów domen
│   │   │   ├── get-domain/ # API domen do skanowania bezpieczeństwa
│   │   │   └── [id]/    # API szczegółów domeny
│   │   │       ├── contact/ # API danych kontaktowych domeny
│   │   │       ├── review/  # API akceptacji/odrzucenia domeny
│   │   │       ├── audit/   # API zapisywania treści audytu domeny
│   │   │       └── route.ts # API CRUD operacji na domenie
│   │   ├── categories/  # API zarządzania kategoriami
│   │   ├── analytics/   # API statystyk i analityki
│   │   ├── logs/        # API logów systemowych
│   │   ├── email-tracking/ # API śledzenia maili
│   │   │   ├── route.ts # Lista eventów trackingu
│   │   │   └── stats/   # Statystyki trackingu
│   │   └── mail-tracking/ # API obsługi trackingu
│   │       └── [...path]/ # Pixel tracking i przekierowania
│   └── globals.css      # Style globalne
├── components/
│   ├── ui/              # Komponenty shadcn/ui
│   ├── auth/            # Komponenty autoryzacji (LoginForm, RegisterForm)
│   ├── dashboard/       # Komponenty dashboard
│   ├── domain/          # Komponenty związane z domenami (MetadataManager, EmailHistory)
│   └── layout/          # Komponenty layoutu (DashboardLayout, TopNavigation)
├── lib/
│   ├── auth.ts          # Konfiguracja NextAuth.js
│   ├── db.ts            # Połączenie z MongoDB
│   ├── cms-checker.ts   # Sprawdzanie typu CMS domen
│   ├── email.ts         # Funkcje wysyłania maili (z trackingiem)
│   ├── mail-tracking.ts # Konfiguracja i obsługa trackingu maili
│   └── utils.ts         # Funkcje pomocnicze
├── models/
│   ├── User.ts          # Model użytkownika MongoDB
│   ├── SearchQuery.ts   # Model zapytań wyszukiwania
│   ├── Link.ts          # Model linków
│   ├── Domain.ts        # Model domen
│   ├── Category.ts      # Model kategorii
│   ├── UserSettings.ts  # Model ustawień użytkownika
│   ├── SearchStatistics.ts # Model statystyk wyszukiwania
│   ├── EmailHistory.ts  # Model historii wysłanych maili audytowych
│   ├── EmailTracking.ts # Model eventów trackingu maili
│   └── Log.ts           # Model logów systemowych
├── types/
│   └── next-auth.d.ts   # Rozszerzenia typów NextAuth.js
└── middleware.ts        # Middleware ochrony tras
```

## Funkcjonalności

### Autoryzacja

- Rejestracja nowych użytkowników z walidacją
- Logowanie z wykorzystaniem email/hasło
- Hashowanie haseł z bcryptjs
- Sesje JWT z NextAuth.js
- Ochrona tras middleware

### Dashboard

- Responsywne górne menu nawigacyjne z linkami do wszystkich sekcji
- Prosta strona główna z powitaniem użytkownika
- Wylogowanie przez menu nawigacyjne
- Dostęp do wszystkich funkcjonalności przez nawigację
- **Ostatnio aktualizowane domeny** - sekcja z 7 najnowszymi domenami na głównej stronie
  - Wyświetlanie nazwy domeny, kategorii, statusu i CMS
  - Znaczniki czasowe z datą ostatniej aktualizacji
  - Kolorowe statusy dla lepszej wizualizacji
  - Linki do szczegółów każdej domeny
  - **Zoptymalizowany limit** - wyświetlanie 7 domen dla optymalnego przeglądu

### Agregator Wyszukiwania

- Integracja z Serper API do wyszukiwania Google z obsługą lokalizacji
- Formularz z polami query, kategoria, lokalizacja i strona (opcjonalnie)
- **Pole lokalizacji** - możliwość określenia lokalizacji wyszukiwania (np. "Warsaw, Poland")
- **Pole strony** - możliwość ręcznego wyboru konkretnej strony Google (1-100)
- **Inteligentna paginacja** - automatyczne zwiększanie numeru strony dla powtarzających się zapytań lub ręczny wybór strony
- Zapisywanie wyników wyszukiwania w bazie danych z informacją o lokalizacji
- Przetwarzanie linków na domeny z protokołem
- Kategoryzowanie wyników według użytkownika
- Ręczne dodawanie domen przez modal z formularzem
- **Okno loading modal** - blokujące okno z animacją ładowania podczas wyszukiwania
  - Animowany spinner z wielowarstwową animacją
  - Informacja o trwającym procesie pobierania linków

#### Tryby paginacji:

**1. Automatyczna paginacja (pole strony puste):**
```
Wyszukiwanie 1: "React tutorial" → Strona 1 (automatycznie)
Wyszukiwanie 2: "React tutorial" → Strona 2 (automatycznie)
Wyszukiwanie 3: "React tutorial" → Strona 3 (automatycznie)
```

**2. Ręczny wybór strony:**
```
Wyszukiwanie: "React tutorial" + Strona: 5 → Strona 5 (wybrana ręcznie)
Wyszukiwanie: "React tutorial" + Strona: 10 → Strona 10 (wybrana ręcznie)
```
  - Przycisk anulowania wyszukiwania
  - Automatyczne zamknięcie po zakończeniu z komunikatem o rezultacie

### Przeglądanie Domen

- Lista wszystkich domen użytkownika z paginacją
- Wyszukiwanie domen po nazwie
- Filtrowanie po kategorii
- Sortowanie (liczba linków, nazwa, data utworzenia)
- Szczegóły domeny z metadanymi i powiązanymi linkami
- Statystyki domen i linków
- **Pasek boczny z nowymi domenami WordPress** - w szczegółach domeny wyświetlany jest stały pasek po prawej stronie z listą domen ze statusem "nowy" i CMS WordPress
  - Wyświetlanie do 20 nowych domen WordPress z możliwością przejścia do pełnej listy
  - Pełna wysokość ekranu z position sticky dla stałej widoczności podczas przewijania
  - Ulepszone odstępy i design z lepszą czytelnością
  - Informacje o liczbie linków, CMS i ocenie dla każdej domeny
  - Szybki dostęp do szczegółów innych domen
  - Responsywny design - pasek ukryty na mniejszych ekranach (poniżej XL)
- **System statusów domen** - domeny mają status: new, accepted, rejected, closed, in_progress, in_audit, production, hold
  - `new` - nowa domena, gotowa do przeglądu
  - `in_progress` - domena jest obecnie obsługiwana przez człowieka
  - `accepted` - domena została zaakceptowana
  - `rejected` - domena została odrzucona
  - `closed` - domena została zamknięta
  - `in_audit` - domena w trakcie audytu bezpieczeństwa
  - `production` - domena produkcyjna
  - `hold` - domena wstrzymana
- Odrzucone domeny (status: rejected) wyświetlane z 40% przezroczystością
- Przycisk do zmiany statusu w szczegółach domeny

### Zarządzanie Kategoriami

- Dodawanie nowych kategorii z nazwą i opisem
- Edycja istniejących kategorii
- Usuwanie kategorii (z wyjątkiem domyślnych)
- Walidacja unikalności nazw kategorii per użytkownik
- Integracja z formularzem wyszukiwania (dropdown select)

### Zarządzanie Linkami

- Przeglądanie wszystkich zapisanych linków z informacją o lokalizacji
- Filtrowanie po kategorii, zapytaniu wyszukiwania i lokalizacji
- **Filtr lokalizacji** - możliwość filtrowania linków po lokalizacji wyszukiwania
- Paginacja wyników
- Wyświetlanie metadanych (tytuł, snippet, URL, lokalizacja)
- Sortowanie według daty dodania

### Analiza Domen

- Agregacja domen z linków według kategorii
- Liczenie linków per domena
- Sortowanie według liczby linków, nazwy domeny lub daty
- Statystyki per kategoria
- Filtrowanie i paginacja

### Helpers (Tymczasowe)

- **Tworzenie domen z linków** - automatyczne tworzenie domen na podstawie istniejących linków
- **Analiza CMS** - sprawdzanie typu CMS dla nowo utworzonych domen (WordPress vs inne)
- **Statystyki tworzenia** - podsumowanie liczby utworzonych domen, WordPress vs inne
- **Monitoring czasu** - śledzenie czasu trwania operacji tworzenia
- **Wyniki w czasie rzeczywistym** - podgląd wyników tworzenia domen z statusami i błędami
- **Interfejs użytkownika** - przycisk do uruchamiania procesu z informacjami o statusie
- Strona tymczasowa, planowana do usunięcia w przyszłości

### Automatyczne sprawdzanie CMS

- **Podczas tworzenia domeny** - CMS jest automatycznie sprawdzany dla każdej nowej domeny
- **Ręczne dodawanie domen** - CMS sprawdzany natychmiast po dodaniu przez formularz
- **Agregator** - CMS sprawdzany dla wszystkich nowych domen z wyników wyszukiwania
- **Wykrywanie WordPress** - testowanie dostępności `/wp-login.php` endpoint
- **Fallback** - domeny oznaczane jako 'inny' w przypadku błędu lub braku WordPress

### Funkcje API

- **Filtrowanie po kategorii** - wszystkie endpointy z parametrem `category` obsługują wyszukiwanie case-insensitive (np. `category=test` znajdzie domeny z kategorią "Test", "TEST", "test")
- **Paginacja** - większość endpointów obsługuje parametry `page` i `limit`
- **Sortowanie** - elastyczne opcje sortowania z parametrami `sortBy` i `sortOrder`

### Zarządzanie Domenami

- **Pole status** - każda domena ma pole `status` z wartościami: 'new', 'accepted', 'rejected', 'closed', 'in_progress'
- **GET /api/domains** - główny endpoint do pobierania domen użytkownika z paginacją i filtrowaniem
  - **UWAGA**: Endpoint nie zwraca treści audytu (`auditContent`) ani danych lighthouse (`lighthouseContent`) ze względów bezpieczeństwa
- **GET /api/domains/[id]** - pobiera szczegóły konkretnej domeny
  - **UWAGA**: Endpoint nie zwraca treści audytu (`auditContent`) ani danych lighthouse (`lighthouseContent`) ze względów bezpieczeństwa
- **GET /api/domains/new-count** - zwraca liczbę domen ze statusem 'new'
- **GET /api/domains/get-domain** - zwraca domeny ze statusem 'new' i CMS 'wordpress' do skanowania bezpieczeństwa
  - Parametr `limit` (opcjonalny) - określa ile domen zwrócić (brak parametru = wszystkie)
  - Parametr `category` (opcjonalny) - filtruje domeny po kategorii (case-insensitive)
  - Parametr `id` (opcjonalny) - zwraca tylko domenę o podanym ID (jeśli spełnia warunki audytu)
  - **AUTOMATYCZNIE dodaje metadata audytu** - ustawia `hasAudit: true`, `dateAudit` dla domen które ich nie mają
  - **Zwiększa liczbę audytów** - domeny są od razu liczone w statystykach po pierwszym pobraniu
  - Zwraca: domains[], count, totalCount, message
  - **UWAGA**: Endpoint nie zwraca treści audytu (`auditContent`) ani danych lighthouse (`lighthouseContent`) ze względów bezpieczeństwa
- **POST /api/domains/[id]/audit** - zapisuje treść audytu domeny (publiczny dostęp)
  - Body: `{ "content": "string" }` - treść audytu do zapisania
  - Zapisuje `auditContent` w rekordzie domeny
  - Ustawia w metadata: `hasAudit: true`
  - **Zachowuje oryginalną datę audytu** - jeśli `dateAudit` już istnieje (z eexecute-scan), nie nadpisuje jej
  - Zwraca: success, message, domain (z id, auditContent, metadata)
- **POST /api/domains/[id]/execute-scan** - wykonuje audyt domeny (chroniony dostęp)
  - Wysyła dane domeny na zewnętrzny webhook (SCAN_WEEBHOOK_URL)
  - Sprawdza czy domena nie ma już audytu
  - Aktualizuje status domeny na 'in_audit'
  - **NATYCHMIAST zwiększa liczbę audytów** - ustawia metadata: `hasAudit: true`, `dateAudit: dzisiejsza data`
  - Zwraca: success, message, data (z domainId, domain, status)
  - **Audyt jest od razu liczony w statystykach** (wykres audytów z ostatnich 30 dni)
- **GET /api/domains/[id]/audit-content** - pobiera treść audytu i lighthouse (chroniony dostęp)
  - Wymaga autoryzacji (sesja użytkownika)
  - Zwraca: success, auditContent, lighthouseContent
  - Używany przez interfejs użytkownika do wyświetlania treści audytów
- **POST /api/domains/[id]/lighthouse** - zapisuje wyniki audytu Lighthouse domeny (publiczny dostęp)
  - Body: `{ "content": "string" }` - wyniki audytu Lighthouse do zapisania
  - Zapisuje `lighthouseContent` w rekordzie domeny
  - Ustawia w metadata: `hasLighthouse: true`, `dateLighthouse: ISO date`
  - Zwraca: success, message, domain (z id, lighthouseContent, metadata)
- **POST /api/domains/[id]/send-audit-email** - wysyła email z audytem i zapisuje w historii (chroniony dostęp)
  - Body: `{ "recipientEmail": "string", "subject": "string" }` - dane emaila
  - **Obsługa wielu adresów**: Jeśli recipientEmail zawiera wiele adresów oddzielonych przecinkami, używany jest tylko pierwszy
  - Automatycznie tworzy wpis w EmailHistory przed wysyłką
  - Zapisuje pełną treść HTML wysłanego maila w polu emailContent
  - Aktualizuje status na 'sent' lub 'failed' po próbie wysyłki
  - Zwraca: success, message, details (z emailHistoryId)
- **GET /api/domains/[id]/email-history** - pobiera historię maili dla domeny (chroniony dostęp)
  - Query params: page, limit, status, emailType
  - Filtry: status (sent/failed/pending), emailType (audit/notification/other)
  - Paginacja: domyślnie 10 wpisów na stronę
  - Zwraca: emailHistory[] (z polem hasContent), pagination, domain info
- **GET /api/domains/[id]/email-history/[historyId]** - pobiera pełną treść konkretnego maila (chroniony dostęp)
  - Zwraca: pełne dane wpisu wraz z emailContent
  - Używane przez modal podglądu treści maila
- **DELETE /api/domains/[id]/email-history?historyId=[id]** - usuwa wpis z historii (chroniony dostęp)
  - Wymaga historyId w query params
  - Weryfikuje własność domeny i wpisu
  - Zwraca: potwierdzenie usunięcia

#### Dane Kontaktowe Domen

- **Formularz kontaktowy** - pod iframe z podglądem domeny
- **Ręczne zapisywanie** - dane zapisywane po kliknięciu przycisku "Zapisz dane kontaktowe"
- **Pola kontaktowe**:
  - Nazwa firmy/organizacji
  - Osoba kontaktowa
  - Email
  - Telefon
  - Adres
  - Notatki
- **GET /api/domains/[id]/contact** - pobiera dane kontaktowe domeny
- **PATCH /api/domains/[id]/contact** - aktualizuje dane kontaktowe domeny
- **Wskaźnik zapisywania** - ikona ładowania podczas zapisywania danych

#### Ustawienia Email

- **Testowy email** - konfigurowalny adres email do wysyłania testowych maili z audytami
- **Strona ustawień** - `/dashboard/ustawienia` zawiera sekcję "Ustawienia Email"
- **API endpoints**:
  - **GET /api/settings/test-email** - pobiera skonfigurowany testowy email (chroniony dostęp)
  - **PUT /api/settings/test-email** - aktualizuje testowy email (chroniony dostęp)
    - Body: `{ "testEmail": "string" }` - nowy adres testowy (opcjonalny)
    - Walidacja formatu email
    - Zwraca: success, message, testEmail
- **Funkcjonalność**:
  - Przycisk "Wyślij testowego maila" w edytorze audytu
  - Automatyczne pobieranie testowego emaila przy ładowaniu strony audytu
  - Walidacja - przycisk nieaktywny gdy brak skonfigurowanego emaila
  - Informacyjny tooltip gdy email nie jest skonfigurowany

#### Wyniki Lighthouse jako Markdown

- **Renderowanie markdown** - wyniki Lighthouse wyświetlane jako sformatowany markdown
- **GitHub Flavored Markdown** - obsługa tabel, strikethrough, checkboxes
- **Responsywne tabele** - automatyczne przewijanie na małych ekranach
- **Niestandardowe stylowanie** - kod, tabele i inne elementy stylowane zgodnie z motywem
- **Obsługa motywów** - automatyczne dostosowanie do ciemnego/jasnego motywu
- **Biblioteki**: React Markdown 10.1.0, Remark GFM 4.0.1, Tailwind Typography 0.5.16

### Analityka i Statystyki

- **API analytics** - `/api/analytics` dostarcza kompleksowe statystyki wyszukiwania
- **Statystyki ogólne** - łączna liczba wyszukiwań, linków, domen
- **Porównanie wyników** - porównanie ostatnich dwóch wyszukiwań z trendami
- **Statystyki kategorii** - analiza wyników per kategoria
- **Trendy czasowe** - statystyki dzienne z ostatnich 30 dni
- **Wskaźniki wydajności** - wskaźnik odrzucenia, liczba przetworzonych wyników
- **Model SearchStatistics** - przechowywanie metadanych każdego wyszukiwania

### Statystyki Audytów

- **POST /api/domains/[id]/audit** - automatyczne zapisywanie informacji o audytach
  - Przy tworzeniu audytu automatycznie ustawiane: `metadata.hasAudit: true`, `metadata.dateAudit: currentDate`
  - Zapisywana treść audytu w polu `auditContent`
  - Dane te są następnie wykorzystywane przez wykres statystyk
  - **Integracja ze skryptem**: Skrypt `wpprobe_audit_script.sh` automatycznie wywołuje ten endpoint po zakończeniu audytu
- **GET /api/dashboard/audit-stats** - chroniony endpoint do pobierania statystyk audytów z ostatnich 30 dni
  - Wymaga autoryzacji (sesja użytkownika)
  - Zwraca dane dla wykresu słupkowego z liczbą audytów dla każdego dnia
  - Agreguje dane na podstawie pola `metadata.dateAudit` w modelu Domain
  - Zawiera przycisk odświeżania danych z cache-busting headers
  - Debugowanie: sprawdza czy audyty są poprawnie liczone i wyświetlane
  - Format odpowiedzi: `{ success, data: [{ date, label, audits }], summary: { totalAudits, period, startDate, endDate } }`

#### Integracja ze skryptem audytu

- **Skrypt**: `scan/wpprobe_audit_script.sh` - automatyczny skrypt audytu bezpieczeństwa WordPress
- **Przepływ danych**:
  1. Skrypt pobiera domeny do skanowania z `/api/domains/get-domain`
  2. Wykonuje skan bezpieczeństwa używając `wpprobe` i `lighthouse`
  3. Wysyła wyniki na zewnętrzny webhook (n8n)
  4. **Automatycznie generuje i zapisuje raport audytu** przez `/api/domains/[id]/audit`
  5. Aktualizuje statystyki audytów w aplikacji
- **Funkcje skryptu**:
  - `generate_audit_content()` - generuje sformatowany raport audytu w Markdown
  - `save_audit_to_app()` - zapisuje raport w aplikacji przez API
  - Obsługa błędów i logowanie wszystkich operacji
- **Konfiguracja**: `AUDIT_API_ENDPOINT` w skrypcie wskazuje na API aplikacji
- **Komponent AuditChart** - wykres słupkowy na dashboardzie pokazujący audyty z ostatnich 30 dni
  - Używa biblioteki Recharts do renderowania wykresu
  - Responsywny design z tooltipami i animacjami
  - Automatyczne ładowanie danych z API
  - Obsługa stanów ładowania i błędów
  - Wyświetlanie łącznej liczby audytów w nagłówku
  - Zwiększona wysokość wykresu (400px) dla lepszej czytelności
  - Inteligentne formatowanie etykiet osi X dla 30 dni danych
  - Szczegółowe tooltips z pełną datą w języku polskim
  - Poprawione kolory słupków - używa `--chart-1` dla lepszej widoczności w ciemnym motywie

### Unikalność Domen

- **Globalna unikalność domen** - każda domena może istnieć tylko raz dla danego użytkownika
  - Wcześniej: domeny mogły się powtarzać w różnych kategoriach
  - Teraz: domeny są unikalne globalnie dla użytkownika
- **Indeks unikalności** - zmieniono indeks z `{ userId, fullDomain, category }` na `{ userId, fullDomain }`
- **API do zarządzania duplikatami**:
  - **GET /api/domains/check-duplicates** - sprawdza i analizuje duplikaty domen
    - Zwraca statystyki, grupy duplikatów i analizę kategorii
  - **POST /api/domains/check-duplicates** - usuwa duplikaty według wybranej strategii
    - Strategie: `keep-oldest`, `keep-newest`, `keep-highest-links`
    - Tryb testowy (`dryRun: true`) do sprawdzenia przed faktycznym usunięciem
- **Skrypt migracji** - `scripts/migrate-domain-uniqueness.js`
  - Sprawdza duplikaty przed migracją
  - Usuwa stary indeks i tworzy nowy
  - Dodaje dodatkowe indeksy dla wydajności
- **Dokumentacja** - szczegółowy opis zmian w `docs/api-relations.txt`

### System Logów

- **Model Log** - przechowywanie logów systemowych z poziomami: info, warn, error, debug
- **POST /api/logs** - publiczny endpoint do dodawania logów (bez autoryzacji)
  - Parametry: level, message, source (opcjonalny), userId (opcjonalny), metadata (opcjonalny)
  - Walidacja poziomów logów i wymaganych pól
  - Zwraca: success, message, logId
  - **Obsługa HTML** - wiadomości mogą zawierać kod HTML (strong, em, code, pre, br, ul, li, span z stylami)
- **GET /api/logs** - chroniony endpoint do pobierania logów (wymaga autoryzacji)
  - Parametry: page, limit, level (filtr), source (filtr), userId (filtr)
  - Paginacja wyników z metadanymi
  - Sortowanie według daty utworzenia (najnowsze pierwsze)
- **DELETE /api/logs** - chroniony endpoint do usuwania wszystkich logów (wymaga autoryzacji)
  - Usuwa wszystkie logi z bazy danych
  - Zwraca: success, message, deletedCount
  - Zabezpieczenia: autoryzacja, potwierdzenie akcji
- **Dashboard logów** - `/dashboard/logs` - przeglądanie logów systemowych
  - Tabela z poziomami, wiadomościami, źródłami i datami
  - **Renderowanie HTML** - wiadomości wyświetlane jako kod HTML z formatowaniem
  - Filtry po poziomie i źródle z opcją ukrywania/pokazywania
  - Paginacja wyników
  - Kolorowe oznaczenia poziomów logów z ikonami
  - **Przycisk "Wyczyść wszystkie logi"** - usuwa wszystkie logi z potwierdzeniem
- **Ostatnie logi na dashboard** - sekcja z 100 najnowszymi logami na głównej stronie
  - **Szerokość 50%** - sekcja zajmuje połowę szerokości strony w układzie dwukolumnowym
  - **Przewijanie** - maksymalna wysokość z przewijaniem dla lepszej obsługi dużej liczby logów
  - Kompaktowy widok z poziomami, wiadomościami i datami
  - **Obsługa HTML** - formatowanie tekstu w wiadomościach logów
  - **Aktualizacja w czasie rzeczywistym** - automatyczne odświeżanie co 10 sekund
  - **Wskaźnik aktywności** - zielona kropka podczas odświeżania w tle
  - **Ręczne odświeżanie** - przycisk do natychmiastowej aktualizacji
  - **Znacznik czasu** - wyświetlanie czasu ostatniej aktualizacji
  - **Sortowanie** - najnowsze logi wyświetlane na górze
  - Link do pełnej strony logów

### Historia Maili Audytowych

- **Automatyczne zapisywanie** - każda wysyłka maila audytowego jest automatycznie zapisywana w historii
- **Komponent EmailHistory** - wyświetlanie historii w szczegółach domeny
  - Tabela z datą wysyłki, odbiorcą, tematem, typem i statusem
  - Filtry po statusie (wysłane/błędy/oczekujące) i typie emaila
  - Paginacja z nawigacją (10 wpisów na stronę)
  - **Podgląd treści maila** - przycisk z ikoną oka dla wpisów z treścią
  - Modal z pełną treścią HTML wysłanego maila
  - Bezpieczne wyświetlanie HTML w iframe z sandbox
  - Opcja otwarcia treści w nowym oknie przeglądarki
  - Możliwość usuwania wpisów z potwierdzeniem
  - Kolorowe badges dla statusów i typów maili
  - Responsywny design z ikonami
- **Metadane wysyłki** - zapisywanie dodatkowych informacji
  - Pełna treść HTML wysłanego maila (max 50000 znaków)
  - Długość treści audytu
  - ID wiadomości (messageId)
  - Komunikaty błędów w przypadku niepowodzenia
- **Bezpieczeństwo** - wszystkie operacje wymagają autoryzacji i weryfikacji własności domeny
- **Integracja** - pełna integracja z istniejącym systemem wysyłki maili audytowych

### Śledzenie Maili (Mail Tracking)

- **Automatyczne śledzenie** - wszystkie maile audytowe wysyłane z włączonym trackingiem
- **Pixel tracking** - niewidoczny pixel do śledzenia otwarć maili
- **Link tracking** - automatyczne przekierowania przez system trackingu dla wszystkich linków
- **Eventy trackingu**:
  - `sent` - mail został wysłany
  - `opened` - mail został otwarty (załadowanie pixela)
  - `clicked` - kliknięcie w link w mailu
  - `delivered` - mail został dostarczony (przyszłe rozszerzenie)
  - `bounced` - mail został odrzucony (przyszłe rozszerzenie)
  - `complained` - zgłoszenie spamu (przyszłe rozszerzenie)
- **Dashboard śledzenia** - `/dashboard/email-tracking`
  - Statystyki: wysłane maile, wskaźnik otwarć, wskaźnik kliknięć
  - Tabela wszystkich eventów trackingu z filtrowaniem
  - Filtry: typ eventu, email odbiorcy, domena, okres czasowy
  - Paginacja wyników z nawigacją
  - Szczegóły eventów: data, typ, odbiorca, temat maila, dodatkowe dane
- **API endpoints**:
  - `GET /api/email-tracking` - lista eventów trackingu z filtrowaniem
  - `GET /api/email-tracking/stats` - statystyki trackingu
  - `GET /api/mail-tracking/blank-image/[jwt]` - pixel trackingu otwarć
  - `GET /api/mail-tracking/link/[jwt]` - przekierowania z trackingiem kliknięć
- **Bezpieczeństwo**:
  - JWT tokeny z ograniczoną ważnością (1 rok)
  - Walidacja tokenów przy każdym evencie
  - Graceful handling błędnych tokenów
  - Zbieranie metadanych: User-Agent, IP, timestamp
- **Integracja z nodemailer-mail-tracking**:
  - Automatyczne dodawanie pixela trackingu do HTML maili
  - Automatyczne modyfikowanie linków w mailach
  - Obsługa JWT tokenów z danymi trackingu
  - Kompatybilność z istniejącym systemem EmailHistory

### UI/UX

- Ciemny motyw shadcn/ui
- Responsywny design
- Walidacja formularzy w czasie rzeczywistym
- Komunikaty błędów i sukcesu
- Ładowanie stanów

## Skrypty

- `npm run dev` - Uruchomienie serwera deweloperskiego (z Turbopack)
- `npm run build` - Budowanie aplikacji produkcyjnej
- `npm run start` - Uruchomienie aplikacji produkcyjnej
- `npm run lint` - Sprawdzenie kodu ESLint

## Deployment

Aplikacja może być wdrożona na Vercel, Netlify lub dowolnej platformie obsługującej Next.js.

Pamiętaj o skonfigurowaniu zmiennych środowiskowych w środowisku produkcyjnym.
