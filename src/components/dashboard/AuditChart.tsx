"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON>, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import { FileText, Loader2, AlertCircle, RefreshCw } from "lucide-react";

interface ChartData {
  date: string;
  label: string;
  scans: number;
  searches: number;
  domains: number;
}

interface ScanData {
  date: string;
  label: string;
  scans: number;
}

interface SearchData {
  date: string;
  label: string;
  searches: number;
  domains: number;
}

interface ScanStatsResponse {
  success: boolean;
  data: ScanData[];
  summary: {
    totalScans: number;
    period: string;
    startDate: string;
    endDate: string;
  };
  debug?: {
    totalDomains: number;
    domainsWithScans: number;
    userId: string;
    aggregationResults: number;
  };
}

interface SearchStatsResponse {
  success: boolean;
  data: SearchData[];
  summary: {
    totalSearches: number;
    totalDomains: number;
    period: string;
    startDate: string;
    endDate: string;
  };
}

interface CustomTooltipProps {
  active?: boolean;
  payload?: Array<{
    value: number;
    payload: ChartData;
    dataKey: string;
  }>;
  label?: string;
}

const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    // Formatuj pełną datę dla tooltipa
    const date = new Date(data.date);
    const fullDate = date.toLocaleDateString('pl-PL', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Znajdź, który słupek jest aktywny
    const activeDataKey = payload[0].dataKey;

    return (
      <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
        <p className="font-medium text-foreground">{fullDate}</p>
        <div className="space-y-1">
          {payload.map((entry) => (
            <p key={entry.dataKey} className={`flex items-center gap-2 ${
              entry.dataKey === 'scans' ? 'text-blue-600' : 'text-green-600'
            }`}>
              <span className={`w-3 h-3 rounded-full ${
                entry.dataKey === 'scans' ? 'bg-blue-600' : 'bg-green-600'
              }`}></span>
              <span className="font-semibold">{entry.value}</span> {entry.dataKey === 'scans' ? 'skanów' : 'wyszukiwań'}
            </p>
          ))}
          {activeDataKey === 'searches' && data.domains > 0 && (
            <p className="text-purple-600 flex items-center gap-2 text-sm italic">
              <span className="w-3 h-3 bg-purple-600 rounded-full"></span>
              <span className="font-semibold">{data.domains}</span> domen dodanych podczas wyszukiwań
            </p>
          )}
        </div>
      </div>
    );
  }
  return null;
};

export default function ScanChart() {
  const [data, setData] = useState<ChartData[]>([]);
  const [scanSummary, setScanSummary] = useState<ScanStatsResponse['summary'] | null>(null);
  const [searchSummary, setSearchSummary] = useState<SearchStatsResponse['summary'] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const fetchStats = async (isRefresh = false) => {
    if (isRefresh) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }
    setError(null);

    try {
      // Fetch both scan and search statistics concurrently
      const [scanResponse, searchResponse] = await Promise.all([
        fetch("/api/dashboard/audit-stats", {
          cache: 'no-cache',
          headers: {
            'Cache-Control': 'no-cache'
          }
        }),
        fetch("/api/dashboard/search-stats", {
          cache: 'no-cache',
          headers: {
            'Cache-Control': 'no-cache'
          }
        })
      ]);

      if (!scanResponse.ok || !searchResponse.ok) {
        throw new Error(`HTTP error! scan status: ${scanResponse.status}, search status: ${searchResponse.status}`);
      }

      const [scanResult, searchResult]: [ScanStatsResponse, SearchStatsResponse] = await Promise.all([
        scanResponse.json(),
        searchResponse.json()
      ]);

      if (scanResult.success && searchResult.success) {
        // Combine the data from both APIs
        const combinedData: ChartData[] = scanResult.data.map(scanDay => {
          const searchDay = searchResult.data.find(s => s.date === scanDay.date);
          return {
            date: scanDay.date,
            label: scanDay.label,
            scans: scanDay.scans,
            searches: searchDay?.searches || 0,
            domains: searchDay?.domains || 0
          };
        });

        console.log('🔍 ScanChart - Combined data:', {
          totalScans: scanResult.summary?.totalScans,
          totalSearches: searchResult.summary?.totalSearches,
          totalDomains: searchResult.summary?.totalDomains,
          dataLength: combinedData.length
        });

        setData(combinedData);
        setScanSummary(scanResult.summary);
        setSearchSummary(searchResult.summary);
      } else {
        throw new Error("Failed to fetch statistics");
      }
    } catch (e) {
      console.error("Failed to fetch stats:", e);
      setError("Nie udało się załadować statystyk. Spróbuj ponownie później.");
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Skany i wyszukiwania z ostatnich 30 dni
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="ml-2 text-muted-foreground">Ładowanie danych...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Skany i wyszukiwania z ostatnich 30 dni
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8 text-destructive">
            <AlertCircle className="h-8 w-8 mr-2" />
            <p>{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Skany i wyszukiwania z ostatnich 30 dni
          </span>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchStats(true)}
              disabled={refreshing}
              className="h-8"
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
            <div className="flex items-center gap-4">
              {scanSummary && (
                <div className="flex items-center gap-2">
                  <span className="w-3 h-3 bg-blue-600 rounded-full"></span>
                  <span className="text-lg font-bold text-blue-600">
                    {scanSummary.totalScans}
                  </span>
                </div>
              )}
              {searchSummary && (
                <div className="flex items-center gap-2">
                  <span className="w-3 h-3 bg-green-600 rounded-full"></span>
                  <span className="text-lg font-bold text-green-600">
                    {searchSummary.totalSearches}
                  </span>
                </div>
              )}
            </div>
          </div>
        </CardTitle>
        {(scanSummary || searchSummary) && (
          <div className="text-sm text-muted-foreground space-y-1">
            {scanSummary && (
              <p>Łącznie {scanSummary.totalScans} skanów przeprowadzonych w ciągu ostatnich {scanSummary.period}</p>
            )}
            {searchSummary && (
              <p>Łącznie {searchSummary.totalSearches} wyszukiwań wykonanych w ciągu ostatnich {searchSummary.period}</p>
            )}
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="h-[400px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={data}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis
                dataKey="label"
                tick={{ fontSize: 10 }}
                angle={-45}
                textAnchor="end"
                height={80}
                interval="preserveStartEnd"
              />
              <YAxis
                tick={{ fontSize: 12 }}
                allowDecimals={false}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar
                dataKey="scans"
                fill="#2563eb"
                radius={[4, 4, 0, 0]}
                className="hover:opacity-80 transition-opacity"
                name="Skany"
              />
              <Bar
                dataKey="searches"
                fill="#16a34a"
                radius={[4, 4, 0, 0]}
                className="hover:opacity-80 transition-opacity"
                name="Wyszukiwania"
              />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {data.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Brak danych z ostatnich 30 dni</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
