"use client";

import { signOut, useSession } from "next-auth/react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import React from "react";
import { usePathname } from "next/navigation";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { ThemeToggle } from "@/components/ui/theme-toggle";

const TopNavigation = React.memo(function TopNavigation() {
  const { data: session } = useSession();
  const pathname = usePathname();

  const handleSignOut = () => {
    signOut({ callbackUrl: "/login" });
  };

  // Funkcje do sprawdzania aktywnej ścieżki
  const isActive = (path: string) => pathname === path;
  const isParentActive = (parentPaths: string[]) =>
    parentPaths.some((path) => pathname.startsWith(path));

  // Funkcja do generowania klas CSS dla linków
  const getLinkClasses = (
    path: string,
    isDropdownParent = false,
    parentPaths: string[] = []
  ) => {
    const baseClasses =
      "px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 hover:bg-accent hover:text-accent-foreground";
    const isCurrentlyActive =
      isActive(path) || (isDropdownParent && isParentActive(parentPaths));

    return cn(
      baseClasses,
      isCurrentlyActive
        ? "bg-accent text-accent-foreground shadow-sm"
        : "text-muted-foreground hover:text-foreground"
    );
  };

  return (
    <nav className="bg-card border-b border-border sticky top-0 z-50">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo i nazwa aplikacji */}
          <div className="flex items-center">
            <Link href="/dashboard" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-lg">
                  AI
                </span>
              </div>
              <span className="text-xl font-bold text-foreground">Studio</span>
            </Link>
          </div>

          {/* Menu nawigacyjne */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-2">
              <Link href="/dashboard" className={getLinkClasses("/dashboard")}>
                Dashboard
              </Link>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className={cn(
                      getLinkClasses("", true, [
                        "/dashboard/domains",
                        "/dashboard/agregator",
                        "/dashboard/email-tracking",
                        "/dashboard/contact-data",
                      ]),
                      "flex items-center gap-1"
                    )}
                  >
                    Domeny
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-48">
                  <DropdownMenuItem asChild>
                    <Link
                      href="/dashboard/domains"
                      className="w-full hover:bg-accent hover:text-accent-foreground transition-colors"
                    >
                      Lista domen
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link
                      href="/dashboard/agregator"
                      className="w-full hover:bg-accent hover:text-accent-foreground transition-colors"
                    >
                      Agregator
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link
                      href="/dashboard/agregator/links"
                      className="w-full hover:bg-accent hover:text-accent-foreground transition-colors"
                    >
                      Zapisane linki
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link
                      href="/dashboard/email-tracking"
                      className="w-full hover:bg-accent hover:text-accent-foreground transition-colors"
                    >
                      Śledzenie maili
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link
                      href="/dashboard/contact-data"
                      className="w-full hover:bg-accent hover:text-accent-foreground transition-colors"
                    >
                      Dane kontaktowe
                    </Link>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className={cn(
                      getLinkClasses("", true, [
                        "/dashboard/ustawienia",
                        "/dashboard/logs",
                        "/dashboard/helpers",
                      ]),
                      "flex items-center gap-1"
                    )}
                  >
                    Ustawienia
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem asChild>
                    <Link
                      href="/dashboard/ustawienia"
                      className="w-full hover:bg-accent hover:text-accent-foreground transition-colors"
                    >
                      Kategorie
                    </Link>
                  </DropdownMenuItem>

                  <DropdownMenuItem asChild>
                    <Link
                      href="/dashboard/logs"
                      className="w-full hover:bg-accent hover:text-accent-foreground transition-colors"
                    >
                      Logi
                    </Link>
                  </DropdownMenuItem>

                  <DropdownMenuItem asChild>
                    <Link
                      href="/dashboard/ustawienia/podatnosci"
                      className="w-full hover:bg-accent hover:text-accent-foreground transition-colors"
                    >
                      Podatności
                    </Link>
                  </DropdownMenuItem>

                  <DropdownMenuItem asChild>
                    <Link
                      href="/dashboard/helpers"
                      className="w-full hover:bg-accent hover:text-accent-foreground transition-colors"
                    >
                      Helpers
                    </Link>
                  </DropdownMenuItem>

                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Informacje o użytkowniku i wylogowanie */}
          <div className="flex items-center space-x-4">
            {session?.user && (
              <div className="hidden md:block text-sm text-muted-foreground">
                Witaj,{" "}
                <span className="text-foreground font-medium">
                  {session.user.name}
                </span>
              </div>
            )}
            <ThemeToggle />
            <Button
              onClick={handleSignOut}
              variant="outline"
              size="sm"
              className="text-sm hover:bg-destructive hover:text-destructive-foreground hover:border-destructive transition-all duration-200"
            >
              Wyloguj
            </Button>
          </div>

          {/* Menu mobilne - przycisk hamburger */}
          <div className="md:hidden">
            <Button variant="ghost" size="sm" className="text-foreground">
              <svg
                className="h-6 w-6"
                stroke="currentColor"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </Button>
          </div>
        </div>
      </div>
    </nav>
  );
});

export default TopNavigation;
