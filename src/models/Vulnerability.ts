import mongoose, { Document, Schema } from 'mongoose';

export interface IVulnerability extends Document {
  vulnerability_id: string;
  vulnerability_name: string;
  volnerability_description: string;
  createdAt: Date;
  updatedAt: Date;
}

const VulnerabilitySchema: Schema = new Schema({
  vulnerability_id: {
    type: String,
    required: [true, 'ID podatności jest wymagane'],
    unique: true,
    trim: true,
    maxlength: [100, 'ID podatności nie może być dłuższe niż 100 znaków']
  },
  vulnerability_name: {
    type: String,
    required: [true, 'Nazwa podatności jest wymagana'],
    trim: true,
    maxlength: [500, 'Nazwa podatności nie może być dłuższa niż 500 znaków']
  },
  volnerability_description: {
    type: String,
    required: [true, 'Opis podatności jest wymagany'],
    trim: true
  }
}, {
  timestamps: true
});

export default mongoose.models.Vulnerability || mongoose.model<IVulnerability>('Vulnerability', VulnerabilitySchema);