import mongoose, { Document, Schema } from 'mongoose';

export interface IContactData extends Document {
  domainId: string;
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  notes: string;
  createdAt: Date;
  updatedAt: Date;
}

const ContactDataSchema: Schema = new Schema({
  domainId: {
    type: String,
    required: [true, 'ID domeny jest wymagane']
  },
  companyName: {
    type: String,
    required: [true, 'Nazwa firmy jest wymagana'],
    trim: true,
    maxlength: [255, 'Nazwa firmy nie może być dłuższa niż 255 znaków']
  },
  contactPerson: {
    type: String,
    required: [true, 'Osoba kontaktowa jest wymagana'],
    trim: true,
    maxlength: [255, 'Osoba kontaktowa nie może być dłuższa niż 255 znaków']
  },
  email: {
    type: String,
    required: [true, 'Email jest wymagany'],
    trim: true,
    maxlength: [500, 'Email nie może być dłu<PERSON>szy niż 500 znaków']
  },
  phone: {
    type: String,
    required: [true, 'Telefon jest wymagany'],
    trim: true,
    maxlength: [50, 'Telefon nie może być dłuższy niż 50 znaków']
  },
  address: {
    type: String,
    required: [true, 'Adres jest wymagany'],
    trim: true,
    maxlength: [500, 'Adres nie może być dłuższy niż 500 znaków']
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, 'Notatki nie mogą być dłuższe niż 1000 znaków'],
    default: ''
  }
}, {
  timestamps: true
});

// Indeks dla domainId (jedna domena może mieć wiele kontaktów)
ContactDataSchema.index({ domainId: 1 });

// Indeks dla szybszego wyszukiwania danych kontaktowych
ContactDataSchema.index({ domainId: 1, createdAt: -1 });

// Indeks dla wyszukiwania po nazwie firmy
ContactDataSchema.index({ domainId: 1, companyName: 1 });

export default mongoose.models.ContactData || mongoose.model<IContactData>('ContactData', ContactDataSchema);