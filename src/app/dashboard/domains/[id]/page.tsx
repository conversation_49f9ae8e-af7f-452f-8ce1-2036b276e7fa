"use client";

import { useState, useEffect, use<PERSON><PERSON>back } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import Link from "next/link";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  <PERSON>ertD<PERSON>og<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON><PERSON><PERSON>,
  <PERSON>ertDialog<PERSON>rigger,
} from "@/components/ui/alert-dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "sonner";
import {
  ArrowLeft,
  ExternalLink,
  Calendar,
  Database,
  CheckCircle,
  Code,
  Server,
  FileText,
  Edit,
  Globe,
  Activity,
  Eye,
  ShieldCheck,
  Trash2,
  Check,
  X,
  ShieldAlert,
  AlertTriangle,
  Clock,
  User,
} from "lucide-react";

import MetadataManager from "@/components/domain/MetadataManager";
import EmailHistory from "@/components/domain/EmailHistory";
import MailTrackingStats from "@/components/domain/MailTrackingStats";
import { RelatedDomainsSidebar } from "@/components/domain/RelatedDomainsSidebar";


interface ContactData {
  id: string;
  domainId: string;
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  notes: string;
  createdAt: string;
  updatedAt: string;
}

interface Domain {
  id: string;
  domain: string;
  protocol: string;
  fullDomain: string;
  category: string;
  linkCount: number;
  status: "new" | "accepted" | "rejected" | "closed" | "in_progress" | "in_audit" | "production" | "hold";
  cms?: string;
  ocena?: "wysoka" | "niska" | "brak";
  auditContent?: string;
  lighthouseContent?: string;
  secureAudit: boolean;
  hasContactData?: boolean;
  metadata?: Record<string, string | number | boolean | null>;
  createdAt: string;
  updatedAt: string;
}

interface DomainDetailsResponse {
  success: boolean;
  domain: Domain;
}

interface Category {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

interface Plugin {
  name: string;
  installed_version: string;
  latest_version: string;
  status: string;
  vulnerabilities_count: number;
}

interface ScanData {
  plugins: Record<string, Plugin>;
}

export default function DomainDetailsPage() {
  const { data: session } = useSession();
  const params = useParams();
  const router = useRouter();
  const [domain, setDomain] = useState<Domain | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSavingDomainInfo, setIsSavingDomainInfo] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [lighthouseContent, setLighthouseContent] = useState<string>("");

  const [iframeError, setIframeError] = useState(false);
  const [showLighthouseModal, setShowLighthouseModal] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [scanData, setScanData] = useState<ScanData | null>(null);
  const [contactData, setContactData] = useState<ContactData[]>([]);

  // Domain info form state
  const [domainInfoData, setDomainInfoData] = useState({
    cms: "",
    status: "",
    category: "",
    ocena: "",
  });

  const [isExecutingScan, setIsExecutingScan] = useState(false);
  const [isExecutingAudit, setIsExecutingAudit] = useState(false);

  const fetchDomainDetails = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/domains/${params.id}`);

      if (!response.ok) {
        if (response.status === 404) {
          toast.error("Domena nie została znaleziona");
          router.push("/dashboard/domains");
          return;
        }
        throw new Error("Błąd pobierania szczegółów domeny");
      }

      const data: DomainDetailsResponse = await response.json();
      setDomain(data.domain);

      // Fetch lighthouse content separately if needed
      const auditResponse = await fetch(`/api/domains/${params.id}/audit-content`);
      if (auditResponse.ok) {
        const auditData = await auditResponse.json();
        if (auditData.success) {
          setLighthouseContent(auditData.lighthouseContent || "");
        }
      }

      // Fetch scan data
      const scanResponse = await fetch(`/api/domains/${params.id}/scan`);
      if (scanResponse.ok) {
        const scanResult = await scanResponse.json();
        if (scanResult.success) {
          setScanData(scanResult.data);
        }
      }

      // Fetch contact data if domain has contact data
      if (data.domain.hasContactData) {
        const contactResponse = await fetch(`/api/domains/${params.id}/contact-data`);
        if (contactResponse.ok) {
          const contactResult = await contactResponse.json();
          if (contactResult.success && contactResult.contactData) {
            setContactData(Array.isArray(contactResult.contactData) ? contactResult.contactData : [contactResult.contactData]);
          }
        }
      }
    } catch (error) {
      console.error("Błąd pobierania szczegółów domeny:", error);
      toast.error("Wystąpił błąd podczas pobierania szczegółów domeny");
    } finally {
      setIsLoading(false);
    }
  }, [params.id, router]);

  const fetchCategories = useCallback(async () => {
    try {
      const response = await fetch("/api/categories");
      if (!response.ok) {
        throw new Error("Błąd pobierania kategorii");
      }
      const data = await response.json();
      setCategories(data.categories);
    } catch (error) {
      console.error("Błąd pobierania kategorii:", error);
      toast.error("Wystąpił błąd podczas pobierania kategorii");
    }
  }, []);

  // Save domain info function
  const saveDomainInfo = async () => {
    if (!domain) return;

    setIsSavingDomainInfo(true);
    try {
      const response = await fetch(`/api/domains/${params.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cms: domainInfoData.cms === "none" ? null : domainInfoData.cms,
          status: domainInfoData.status,
          category: domainInfoData.category,
          ocena: domainInfoData.ocena === "brak" ? null : domainInfoData.ocena,
        }),
      });

      if (!response.ok) {
        throw new Error("Błąd aktualizacji informacji o domenie");
      }

      const data = await response.json();
      setDomain(data.domain);
      toast.success("Informacje o domenie zostały zapisane");
    } catch (error) {
      console.error("Błąd aktualizacji informacji o domenie:", error);
      toast.error("Wystąpił błąd podczas aktualizacji informacji o domenie");
    } finally {
      setIsSavingDomainInfo(false);
    }
  };

  // Handle domain info changes
  const handleDomainInfoChange = (
    field: keyof typeof domainInfoData,
    value: string
  ) => {
    setDomainInfoData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };


  useEffect(() => {
    if (params.id) {
      fetchDomainDetails();
    }
    fetchCategories();
  }, [params.id, fetchDomainDetails, fetchCategories]);

  // Initialize domain info form data when domain is loaded
  useEffect(() => {
    if (domain?.id) {
      setDomainInfoData({
        cms: domain.cms || "none",
        status: domain.status,
        category: domain.category,
        ocena: domain.ocena || "brak",
      });
    }
  }, [domain?.id, domain?.cms, domain?.status, domain?.category, domain?.ocena]);



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pl-PL", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Helper functions for iframe
  const handleIframeError = () => {
    setIframeError(true);
  };

  const openInNewTab = () => {
    if (domain) {
      window.open(domain.fullDomain, "_blank", "noopener,noreferrer");
    }
  };

  const toggleIframeError = () => {
    setIframeError(!iframeError);
  };

  // Execute scan
  const executeAudit = async () => {
    if (!params.id || !domain) return;

    setIsExecutingScan(true);
    try {
      const response = await fetch(`/api/domains/${params.id}/execute-scan`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Błąd wykonywania skanowania");
      }

      const data = await response.json();
      if (data.success) {
        toast.success("Rozpoczęto skanowanie");
        // Odśwież dane domeny
        await fetchDomainDetails();
      }
    } catch (error) {
      console.error("Błąd wykonywania skanowania:", error);
      toast.error(error instanceof Error ? error.message : "Wystąpił błąd podczas wykonywania skanowania");
    } finally {
      setIsExecutingScan(false);
    }
  };

  // Execute audit
  const executeMetadataAudit = async () => {
    if (!params.id || !domain) return;

    setIsExecutingAudit(true);
    try {
      const response = await fetch(`/api/domains/${params.id}/execute-audit`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Błąd wykonywania audytu");
      }

      const data = await response.json();
      if (data.success) {
        toast.success("Generowanie maila");
      }
    } catch (error) {
      console.error("Błąd wykonywania audytu:", error);
      toast.error(error instanceof Error ? error.message : "Wystąpił błąd podczas wykonywania audytu");
    } finally {
      setIsExecutingAudit(false);
    }
  };

  // Update domain status function
  const updateDomainStatus = async (status: string) => {
    if (!params.id || !domain) return;

    setIsUpdatingStatus(true);
    try {
      const response = await fetch(`/api/domains/${params.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Błąd aktualizacji statusu domeny");
      }

      const data = await response.json();
      if (data.success) {
        setDomain(data.domain);
        // Update the form data as well
        setDomainInfoData(prev => ({
          ...prev,
          status: data.domain.status
        }));
        toast.success(data.message);

        // If accepted, navigate to next page with in_progress status
        if (status === 'accepted') {
          setTimeout(() => {
            router.push('/dashboard/domains?status=in_progress');
          }, 1500);
        }
      }
    } catch (error) {
      console.error("Błąd aktualizacji statusu domeny:", error);
      toast.error(error instanceof Error ? error.message : "Wystąpił błąd podczas aktualizacji statusu domeny");
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // Delete domain function
  const handleDeleteDomain = async () => {
    if (!params.id || !domain) return;

    setIsDeleting(true);
    try {
      const response = await fetch(`/api/domains/${params.id}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Błąd usuwania domeny");
      }

      const data = await response.json();
      if (data.success) {
        toast.success(data.message);
        // Przekieruj do listy domen
        router.push("/dashboard/domains");
      }
    } catch (error) {
      console.error("Błąd usuwania domeny:", error);
      toast.error(error instanceof Error ? error.message : "Wystąpił błąd podczas usuwania domeny");
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">
          <p className="text-muted-foreground">
            Ładowanie szczegółów domeny...
          </p>
        </div>
      </div>
    );
  }

  if (!domain) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">
          <p className="text-muted-foreground">
            Domena nie została znaleziona.
          </p>
          <Button asChild className="mt-4">
            <Link href="/dashboard/domains">Powrót do listy domen</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 xl:grid-cols-[1fr_350px] gap-6">
      {/* Main content */}
      <div className="space-y-6">
        {/* Domain Preview and Basic Info */}
        <div className="grid grid-cols-1 lg:grid-cols-[55%_1fr] gap-6">
        {/* Iframe preview */}
        <Card className="pb-0">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/dashboard/domains">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Powrót
                  </Link>
                </Button>
                <Link
                  href={domain.fullDomain}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {domain.domain}
                </Link>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  onClick={toggleIframeError}
                  variant="ghost"
                  size="sm"
                  className="text-xs"
                >
                  {iframeError ? "Pokaż iframe" : "Ukryj iframe"}
                </Button>
                <Button
                  onClick={openInNewTab}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <ExternalLink className="h-4 w-4" />
                  Otwórz w nowej karcie
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            {iframeError ? (
              <div className="p-6 text-center">
                <div className="text-muted-foreground mb-4">
                  <Globe className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>Nie można wyświetlić podglądu domeny</p>
                  <p className="text-sm">
                    Domena może blokować wyświetlanie w ramce
                  </p>
                </div>
                <Button onClick={openInNewTab} variant="outline">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Otwórz {domain.domain} w nowej karcie
                </Button>
              </div>
            ) : (
              <iframe
                src={domain.fullDomain}
                className="w-full h-[72vh] border-0 rounded-b-lg overflow-x-hidden"
                title={`Podgląd domeny ${domain.domain}`}
                sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-top-navigation"
                onError={handleIframeError}
              />
            )}
          </CardContent>
        </Card>

        {/* Informacje podstawowe */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Informacje o domenie
              </div>
              <div className="flex items-center gap-2">

                <Button
                  variant="outline"
                  size="sm"
                  asChild
                  className="flex items-center gap-2"
                >
                  <Link href={`/dashboard/domains/${domain.id}/scan`}>
                    <ShieldCheck className="h-4 w-4" />
                    Skan
                  </Link>
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowLighthouseModal(true)}
                  className="flex items-center gap-2"
                  disabled={!lighthouseContent}
                >
                  <Activity className="h-4 w-4" />
                  Lighthouse
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  asChild
                  className="flex items-center gap-2"
                >
                  <Link href={`/dashboard/domains/${domain.id}/audit`}>
                    <FileText className="h-4 w-4" />
                    Audyt
                  </Link>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  asChild
                  className="flex items-center gap-2"
                >
                  <Link
                    href={`https://audyt-bezpczenstwa.pl/audyty/${domain.id}.html`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Eye className="h-4 w-4" />
                    Podgląd Lighthouse
                  </Link>
                </Button>
                <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="destructive"
                      size="sm"
                      className="flex items-center gap-2"
                    >
                      <Trash2 className="h-4 w-4" />
                      Usuń domenę
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Usuń domenę</AlertDialogTitle>
                      <AlertDialogDescription>
                        Czy na pewno chcesz usunąć domenę <strong>{domain.domain}</strong>?
                        <br />
                        <br />
                        Ta akcja spowoduje również usunięcie wszystkich powiązanych linków i jest nieodwracalna.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel disabled={isDeleting}>
                        Anuluj
                      </AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handleDeleteDomain}
                        disabled={isDeleting}
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                      >
                        {isDeleting ? "Usuwanie..." : "Usuń domenę"}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <p className="text-sm text-muted-foreground">Pełny adres</p>
                <p className="font-medium">{domain.fullDomain}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Kategoria</p>
                <Select
                  value={domainInfoData.category}
                  onValueChange={(value) =>
                    handleDomainInfoChange("category", value)
                  }
                  disabled={isSavingDomainInfo}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.length === 0 ? (
                      <SelectItem value="no-categories" disabled>
                        Brak kategorii. Dodaj kategorie w ustawieniach.
                      </SelectItem>
                    ) : (
                      categories.map((category) => (
                        <SelectItem key={category.id} value={category.name}>
                          {category.name}
                          {category.description && (
                            <span className="text-muted-foreground ml-2">
                              - {category.description}
                            </span>
                          )}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">CMS</p>
                <Select
                  value={domainInfoData.cms}
                  onValueChange={(value) =>
                    handleDomainInfoChange("cms", value)
                  }
                  disabled={isSavingDomainInfo}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">
                      <span className="text-muted-foreground">
                        Nie sprawdzono
                      </span>
                    </SelectItem>
                    <SelectItem value="wordpress">
                      <div className="flex items-center gap-2">
                        <Code className="h-4 w-4 text-blue-600" />
                        <span className="text-blue-600">WordPress</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="inny">
                      <div className="flex items-center gap-2">
                        <Server className="h-4 w-4 text-gray-600" />
                        <span className="text-gray-600">Inny CMS</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Data utworzenia</p>
                <p className="font-medium flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  {formatDate(domain.createdAt)}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">
                  Ostatnia aktualizacja
                </p>
                <p className="font-medium flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  {formatDate(domain.updatedAt)}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Status</p>
                <div className="mt-1">
                  <Select
                    value={domainInfoData.status}
                    onValueChange={(value) =>
                      handleDomainInfoChange("status", value)
                    }
                    disabled={isSavingDomainInfo}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="new">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-gray-500"></div>
                          Nowa
                        </div>
                      </SelectItem>
                  <SelectItem value="in_audit">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                          Skan
                        </div>
                      </SelectItem>
                      <SelectItem value="in_progress">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                          Audty
                        </div>
                      </SelectItem>
                      <SelectItem value="accepted">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-green-500"></div>
                          Zaakceptowana
                        </div>
                      </SelectItem>
                      <SelectItem value="rejected">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-red-500"></div>
                          Odrzucona
                        </div>
                      </SelectItem>
                      <SelectItem value="closed">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-gray-400"></div>
                          Zamknięta
                        </div>
                      </SelectItem>
                      <SelectItem value="production">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                          Produkcyjna
                        </div>
                      </SelectItem>
                      <SelectItem value="hold">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                          Wstrzymana
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Dane kontaktowe</p>
                <div className="mt-1">
                  {domain.hasContactData ? (
                    <div className="flex items-center gap-1 text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 px-2 py-1 rounded-md w-fit">
                      <CheckCircle className="h-3 w-3" />
                      Uzupełnione
                    </div>
                  ) : (
                    <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-900/20 dark:text-orange-300 dark:border-orange-800">
                      Brak danych
                    </Badge>
                  )}
                </div>
              </div>

              <div>
                <p className="text-sm text-muted-foreground">Ocena ryzyka ataku</p>
                <Select
                  value={domainInfoData.ocena}
                  onValueChange={(value) =>
                    handleDomainInfoChange("ocena", value)
                  }
                  disabled={isSavingDomainInfo}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="brak">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-gray-400"></div>
                        Brak
                      </div>
                    </SelectItem>
                    <SelectItem value="niska">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-red-500"></div>
                        Niska
                      </div>
                    </SelectItem>
                    <SelectItem value="wysoka">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-green-500"></div>
                        Wysoka
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <p className="text-sm text-muted-foreground">
                  Skan
                </p>
                {domain.secureAudit ? (
                  <div className="flex items-center gap-2 mt-1">
                    <div className="flex items-center gap-1 text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 px-2 py-1 rounded-md w-fit">
                      <ShieldCheck className="h-3 w-3" />
                      Audyt OK
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 pt-0.5">
                    <Button
                      variant="outline"
                      onClick={executeAudit}
                      disabled={isExecutingScan || domain.status === 'in_audit'}
                      className="hover:bg-primary hover:text-primary-foreground hover:cursor-pointer transition-colors"
                    >
                      {isExecutingScan ? (
                        <>
                          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-primary mr-1"></div>
                          Wykonywanie...
                        </>
                      ) : domain.status === 'in_audit' ? (
                        <>
                          <div className="animate-pulse h-3 w-3 rounded-full bg-blue-500 mr-1"></div>
                          W trakcie skanowania
                        </>
                      ) : (
                        <>
                          <ShieldCheck className="h-3 w-3 mr-1" />
                          Wykonaj skan
                        </>
                      )}
                    </Button>

                  </div>
                )}
              </div>

              <div>
                 <p className="text-sm text-muted-foreground">
                  Mail audytowy
                </p>
                            <Button
                      variant="outline"
                      onClick={executeMetadataAudit}
                      disabled={isExecutingAudit}
                      className="hover:bg-secondary hover:text-secondary-foreground hover:cursor-pointer transition-colors"
                    >
                      {isExecutingAudit ? (
                        <>
                          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-primary mr-1"></div>
                          Wykonywanie...
                        </>
                      ) : (
                        <>
                          <FileText className="h-3 w-3 mr-1" />
                          Generuj
                        </>
                      )}
                    </Button>
              </div>



              {/* Save button and status action buttons */}
              <div className="col-span-full flex justify-between items-center pt-4 justify-end">

                <Button
                  onClick={saveDomainInfo}
                  disabled={isSavingDomainInfo}
                  className="flex items-center gap-2"
                >
                  {isSavingDomainInfo ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Zapisywanie...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4" />
                      Zapisz informacje o domenie
                    </>
                  )}
                </Button>
              </div>


            </div>
          </CardContent>
          <CardFooter className="flex flex-col align-end justify-end flex-1 ">
             <div className="w-full">
                           {/* Plugin Statistics Cards - only show if scan data exists */}
              {scanData && scanData.plugins && Object.keys(scanData.plugins).length > 0 && (
                <div className="col-span-full">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">
                          Łączna liczba pluginów
                        </CardTitle>
                        <Database className="h-4 w-4 text-muted-foreground" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {Object.keys(scanData.plugins).length}
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">
                          Nieaktualne pluginy
                        </CardTitle>
                        <Clock className="h-4 w-4 text-yellow-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-yellow-600">
                          {Object.values(scanData.plugins).filter(p => p.status === "Wymaga aktualizacji").length}
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">
                          Pluginy z podatnościami
                        </CardTitle>
                        <ShieldAlert className="h-4 w-4 text-red-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-red-600">
                          {Object.values(scanData.plugins).filter(p => p.vulnerabilities_count > 0).length}
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">
                          Łączne podatności
                        </CardTitle>
                        <AlertTriangle className="h-4 w-4 text-orange-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-orange-600">
                          {Object.values(scanData.plugins).reduce((sum, p) => sum + p.vulnerabilities_count, 0)}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              )}
             </div>
             <div>
             {(domain.status === "in_progress") && (
                    <div className="flex gap-2">
                      <Button
                        onClick={() => updateDomainStatus("accepted")}
                        disabled={isUpdatingStatus}
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-2 text-green-600 border-green-200 hover:bg-green-50"
                      >
                        {isUpdatingStatus ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                        ) : (
                          <Check className="h-4 w-4" />
                        )}
                        Akceptuj
                      </Button>
                      <Button
                        onClick={() => updateDomainStatus("rejected")}
                        disabled={isUpdatingStatus}
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-2 text-red-600 border-red-200 hover:bg-red-50"
                      >
                        {isUpdatingStatus ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                        ) : (
                          <X className="h-4 w-4" />
                        )}
                        Odrzuć
                      </Button>
                    </div>
                  )}

             </div>

          </CardFooter>
        </Card>

        {/* Contact Data Display */}
        {contactData && contactData.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Dane kontaktowe ({contactData.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {contactData.map((contact, index) => (
                  <div key={contact.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-lg font-medium">Kontakt #{index + 1}</h4>
                      <div className="text-sm text-muted-foreground">
                        {formatDate(contact.createdAt)}
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div>
                        <p className="text-sm text-muted-foreground">Nazwa firmy</p>
                        <p className="font-medium">{contact.companyName}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Osoba kontaktowa</p>
                        <p className="font-medium">{contact.contactPerson}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Email</p>
                        <p className="font-medium">{contact.email}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Telefon</p>
                        <p className="font-medium">{contact.phone}</p>
                      </div>
                      <div className="md:col-span-2">
                        <p className="text-sm text-muted-foreground">Adres</p>
                        <p className="font-medium">{contact.address}</p>
                      </div>
                      {contact.notes && (
                        <div className="md:col-span-3">
                          <p className="text-sm text-muted-foreground">Notatki</p>
                          <p className="font-medium">{contact.notes}</p>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Metadata and other components */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

        {/* Metadata Manager */}
        <MetadataManager
          domainId={domain.id}
          initialMetadata={domain.metadata}
          onMetadataUpdate={(metadata) => {
            setDomain((prev) => (prev ? { ...prev, metadata } : null));
          }}
        />

        {/* Email History */}
        <EmailHistory domainId={domain.id} />

        {/* Mail Tracking Stats */}
        <MailTrackingStats domainId={domain.id} />
      </div>

      {/* Lighthouse Modal */}
      <Dialog open={showLighthouseModal} onOpenChange={setShowLighthouseModal}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Wyniki Lighthouse - {domain.domain}
            </DialogTitle>
          </DialogHeader>
          <ScrollArea className="max-h-[60vh] w-full">
            <div className="p-4">
              {lighthouseContent ? (
                <div className="prose prose-sm max-w-none dark:prose-invert">
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm]}
                    components={{
                      // Custom styling for code blocks
                      code: ({ className, children, ...props }: React.ComponentProps<'code'>) => {
                        const isInline = !className?.includes('language-');
                        return isInline ? (
                          <code
                            className="bg-muted px-1 py-0.5 rounded text-sm font-mono"
                            {...props}
                          >
                            {children}
                          </code>
                        ) : (
                          <code
                            className="block bg-muted p-4 rounded-lg overflow-x-auto text-sm font-mono whitespace-pre"
                            {...props}
                          >
                            {children}
                          </code>
                        );
                      },
                      // Custom styling for tables
                      table: ({ children }: React.ComponentProps<'table'>) => (
                        <div className="overflow-x-auto">
                          <table className="min-w-full border-collapse border border-border">
                            {children}
                          </table>
                        </div>
                      ),
                      th: ({ children }: React.ComponentProps<'th'>) => (
                        <th className="border border-border bg-muted px-4 py-2 text-left font-semibold">
                          {children}
                        </th>
                      ),
                      td: ({ children }: React.ComponentProps<'td'>) => (
                        <td className="border border-border px-4 py-2">
                          {children}
                        </td>
                      ),
                    }}
                  >
                    {lighthouseContent}
                  </ReactMarkdown>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Brak danych Lighthouse dla tej domeny</p>
                </div>
              )}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
      </div>

      {/* Related Domains Sidebar */}
      <div className="hidden xl:block">
        <RelatedDomainsSidebar currentDomain={domain} />
      </div>
    </div>
  );
}
