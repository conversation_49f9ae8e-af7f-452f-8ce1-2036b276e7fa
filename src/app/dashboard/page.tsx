"use client";

import React, { useEffect, useState, useCallback } from "react";
import { useSession } from "next-auth/react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import {
  Globe,
  FilePlus,
  CheckCircle2,
  AlertTriangle,
  Loader2,
  FileText,
  Info,
  AlertCircle,
  Bug,
  RefreshCw,
  XCircle,
  Server,
  Shield,
  Link2,
  Clock,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { format, formatDistanceToNow } from "date-fns";
import { pl } from "date-fns/locale";
import Link from "next/link";
import Scan<PERSON><PERSON> from "@/components/dashboard/AuditChart";
import WordPressCategoryChart from "@/components/dashboard/WordPressCategoryChart";

interface RecentDomain {
  id: string;
  domain: string;
  fullDomain: string;
  category: string;
  status: string;
  cms?: string;
  updatedAt: string;
}

interface DashboardStats {
  totalDomains: number;
  newDomains: number;
  acceptedDomains: number;
  rejectedDomains: number;
  inProgressDomains: number;
  wordpressDomains: number;
  otherCmsDomains: number;
  unknownCmsDomains: number;
  totalVulnerabilities: number;
  totalLinks: number;
  averageScanTime: number;
  totalScansWithTime: number;
  wordpressByCategory: Array<{ category: string; count: number }>;
  recentDomains: RecentDomain[];
}

interface StatItem {
  title: string;
  value: number;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  unit?: string;
  subtitle?: string;
}

interface LogEntry {
  _id: string;
  level: "info" | "warn" | "error" | "debug";
  message: string;
  source?: string;
  userId?: string;
  metadata?: Record<string, string | number | boolean>;
  createdAt: string;
  updatedAt: string;
}

const levelIcons = {
  info: Info,
  warn: AlertTriangle,
  error: AlertCircle,
  debug: Bug,
};

const levelColors = {
  info: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  warn: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  error: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
  debug: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
};

export default function DashboardPage() {
  const { data: session } = useSession();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [recentLogs, setRecentLogs] = useState<LogEntry[]>([]);
  const [logsLoading, setLogsLoading] = useState(true);
  const [lastLogUpdate, setLastLogUpdate] = useState<Date>(new Date());
  const [isRefreshing, setIsRefreshing] = useState(false);

  const fetchRecentLogs = useCallback(
    async (showLoading = true) => {
      if (!session) return;
      if (showLoading) {
        setLogsLoading(true);
      } else {
        setIsRefreshing(true);
      }
      try {
        const response = await fetch("/api/logs?limit=100");
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setRecentLogs(data.logs);
            setLastLogUpdate(new Date());
          }
        }
      } catch (e) {
        console.error("Failed to fetch recent logs:", e);
      }
      if (showLoading) {
        setLogsLoading(false);
      } else {
        setIsRefreshing(false);
      }
    },
    [session]
  );

  useEffect(() => {
    const fetchStats = async () => {
      if (!session) return; // Wait for session to be available
      setLoading(true);
      setError(null);
      try {
        // TODO: Replace with actual API endpoint if different
        const response = await fetch("/api/dashboard/stats");
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setStats(data);
      } catch (e) {
        console.error("Failed to fetch dashboard stats:", e);
        setError(
          "Nie udało się załadować statystyk. Spróbuj ponownie później."
        );
      }
      setLoading(false);
    };

    if (session) {
      // Fetch stats once session is loaded
      fetchStats();
      fetchRecentLogs();
    }
  }, [session, fetchRecentLogs]);

  // Real-time logs via Server-Sent Events
  useEffect(() => {
    if (!session) return;

    let eventSource: EventSource | null = null;

    const connectToSSE = () => {
      eventSource = new EventSource('/api/logs/stream');
      
      eventSource.onopen = () => {
        console.log('SSE connection opened');
        setIsRefreshing(false);
      };

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          if (data.type === 'connected') {
            console.log('Connected to log stream');
          } else if (data.type === 'initial') {
            setRecentLogs(data.logs);
            setLastLogUpdate(new Date());
            setLogsLoading(false);
          } else if (data.type === 'new_log') {
            setRecentLogs(prev => [data.log, ...prev.slice(0, 99)]);
            setLastLogUpdate(new Date());
            setIsRefreshing(true);
            setTimeout(() => setIsRefreshing(false), 500); // Brief flash
          }
        } catch (error) {
          console.error('Error parsing SSE data:', error);
        }
      };

      eventSource.onerror = (error) => {
        console.error('SSE connection error:', error);
        eventSource?.close();
        
        // Retry connection after 5 seconds
        setTimeout(connectToSSE, 5000);
      };
    };

    connectToSSE();

    return () => {
      if (eventSource) {
        eventSource.close();
      }
    };
  }, [session]);

  const statItems: StatItem[] = stats
    ? [
        {
          title: "Wszystkie Domeny",
          value: stats.totalDomains,
          icon: Globe,
          color: "text-sky-600",
        },
        {
          title: "Nowe Domeny",
          value: stats.newDomains,
          icon: FilePlus,
          color: "text-amber-600",
        },
        {
          title: "Zaakceptowane Domeny",
          value: stats.acceptedDomains,
          icon: CheckCircle2,
          color: "text-green-600",
        },
        {
          title: "Odrzucone Domeny",
          value: stats.rejectedDomains,
          icon: XCircle,
          color: "text-red-600",
        },
        {
          title: "W Trakcie",
          value: stats.inProgressDomains,
          icon: Loader2,
          color: "text-blue-600",
        },
        {
          title: "WordPress",
          value: stats.wordpressDomains,
          icon: Server,
          color: "text-indigo-600",
        },
        {
          title: "Inne CMS",
          value: stats.otherCmsDomains,
          icon: Server,
          color: "text-purple-600",
        },
        {
          title: "Nieznane CMS",
          value: stats.unknownCmsDomains,
          icon: Server,
          color: "text-gray-600",
        },
        {
          title: "Podatności",
          value: stats.totalVulnerabilities,
          icon: Shield,
          color: "text-orange-600",
        },
        {
          title: "Łączna liczba linków",
          value: stats.totalLinks,
          icon: Link2,
          color: "text-cyan-600",
        },
        {
          title: "Średni czas skanu",
          value: stats.averageScanTime,
          icon: Clock,
          color: "text-pink-600",
          unit: "s",
          subtitle: `Na podstawie ${stats.totalScansWithTime} skanów`,
        },
      ]
    : [];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-foreground">Dashboard Główny</h1>
        <p className="text-muted-foreground">
          Przegląd kluczowych metryk i aktywności w AI Studio.
        </p>
      </div>

      {loading && (
        <div className="flex items-center justify-center py-10">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="ml-2 text-muted-foreground">Ładowanie danych...</p>
        </div>
      )}

      {error && (
        <Card className="bg-destructive/10 border-destructive">
          <CardHeader className="flex flex-row items-center space-x-2 pb-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            <CardTitle className="text-destructive text-lg">Błąd</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-destructive/80">{error}</p>
          </CardContent>
        </Card>
      )}

      {!loading && !error && stats && (
        <div className="grid grid-cols-1">

          {/* Recent Logs Section */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Ostatnie Logi
                  {isRefreshing && (
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  )}
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  100 najnowszych wpisów logów
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  Ostatnia aktualizacja:{" "}
                  {format(lastLogUpdate, "HH:mm:ss", { locale: pl })}
                  <span className="ml-2">• W czasie rzeczywistym</span>
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => fetchRecentLogs(true)}
                  disabled={logsLoading}
                  className="flex items-center gap-1"
                >
                  <RefreshCw
                    className={`h-3 w-3 ${logsLoading ? "animate-spin" : ""}`}
                  />
                  Odśwież
                </Button>
                <Link
                  href="/dashboard/logs"
                  className="text-sm text-primary hover:underline"
                >
                  Zobacz wszystkie
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              {logsLoading ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin text-primary" />
                  <p className="ml-2 text-muted-foreground">
                    Ładowanie logów...
                  </p>
                </div>
              ) : recentLogs.length === 0 ? (
                <p className="text-muted-foreground text-center py-4">
                  Brak ostatnich logów.
                </p>
              ) : (
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {recentLogs.map((log) => {
                    const LevelIcon = levelIcons[log.level] || Info;
                    return (
                      <div
                        key={log._id}
                        className="flex items-start gap-3 p-3 rounded-lg bg-muted/50"
                      >
                        <span className="text-xs text-muted-foreground">
                          {formatDistanceToNow(new Date(log.createdAt), {
                            addSuffix: true,
                            locale: pl,
                          })}
                        </span>
                        <Badge
                          className={levelColors[log.level] || levelColors.info}
                        >
                          <LevelIcon className="h-3 w-3 mr-1" />
                          {log.level?.toUpperCase() || "INFO"}
                        </Badge>
                        <div className="flex-1 min-w-0">
                          <div
                            className="text-sm text-foreground truncate"
                            title={log.message}
                            dangerouslySetInnerHTML={{ __html: log.message }}
                          />
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          {log.source && (
                            <Link href={`/dashboard/domains/${log.source}`} className="text-xs hover:underline hover:text-white">
                              {log.source}
                            </Link>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {!loading && !error && stats && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6">
          {statItems.map((item) => (
            <Card key={item.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {item.title}
                </CardTitle>
                <item.icon className={`h-5 w-5 ${item.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {item.value !== undefined ? item.value : "-"}
                  {item.unit && <span className="text-lg text-muted-foreground ml-1">{item.unit}</span>}
                </div>
                {/* Custom subtitle for specific items */}
                {item.subtitle && (
                  <p className="text-xs text-muted-foreground mt-1">
                    {item.subtitle}
                  </p>
                )}
                {/* Additional info for CMS ratio */}
                {(item.title === "WordPress" || item.title === "Inne CMS" || item.title === "Nieznane CMS") && stats && (
                  <p className="text-xs text-muted-foreground mt-1">
                    {item.title === "WordPress"
                      ? `${((stats.wordpressDomains / (stats.wordpressDomains + stats.otherCmsDomains + stats.unknownCmsDomains)) * 100).toFixed(1)}% wszystkich`
                      : item.title === "Inne CMS"
                      ? `${((stats.otherCmsDomains / (stats.wordpressDomains + stats.otherCmsDomains + stats.unknownCmsDomains)) * 100).toFixed(1)}% wszystkich`
                      : `${((stats.unknownCmsDomains / (stats.wordpressDomains + stats.otherCmsDomains + stats.unknownCmsDomains)) * 100).toFixed(1)}% wszystkich`
                    }
                  </p>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {!loading && !error && !stats && session && (
        <div className="text-center py-8">
          <p className="text-muted-foreground">Brak danych do wyświetlenia.</p>
        </div>
      )}

      {/* Charts Section */}
      {!loading && !error && stats && (
        <div className="grid gap-6 lg:grid-cols-3">
          <ScanChart />
          <WordPressCategoryChart
            data={stats.wordpressByCategory}
            totalWordPressDomains={stats.wordpressDomains}
          />
          {/* Recent Domains Section */}
          {stats.recentDomains && stats.recentDomains.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  Ostatnio Aktualizowane Domeny
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  7 domen z najnowszą datą aktualizacji
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {stats.recentDomains.map((domain) => (
                    <div
                      key={domain.id}
                      className="flex items-center justify-between p-3 rounded-lg bg-muted/50 hover:bg-muted/70 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        <Globe className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <Link
                            href={`/dashboard/domains/${domain.id}`}
                            className="font-medium hover:text-primary transition-colors"
                          >
                            {domain.domain}
                          </Link>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {domain.category}
                            </Badge>
                            <Badge
                              className={`text-xs ${
                                domain.status === "accepted"
                                  ? "bg-green-100 text-green-800"
                                  : domain.status === "new"
                                  ? "bg-yellow-100 text-yellow-800"
                                  : domain.status === "in_progress"
                                  ? "bg-blue-100 text-blue-800"
                                  : domain.status === "rejected"
                                  ? "bg-red-100 text-red-800"
                                  : "bg-gray-100 text-gray-800"
                              }`}
                            >
                              {domain.status}
                            </Badge>
                            {domain.cms && (
                              <Badge variant="secondary" className="text-xs">
                                {domain.cms}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-muted-foreground">
                          {formatDistanceToNow(new Date(domain.updatedAt), {
                            addSuffix: true,
                            locale: pl,
                          })}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {format(
                            new Date(domain.updatedAt),
                            "dd.MM.yyyy HH:mm",
                            { locale: pl }
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

        </div>
      )}

    </div>
  );
}
