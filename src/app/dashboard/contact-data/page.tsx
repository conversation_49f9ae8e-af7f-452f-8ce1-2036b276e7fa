"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Database,
  Search,
  ExternalLink,
  Building,
  User,
  Mail,
  Phone,
  MapPin,
  FileText,
  Calendar,
  Globe,
  Filter,
  Download,
  ChevronDown,
  ChevronUp,
} from "lucide-react";

interface ContactDataWithDomain {
  id: string;
  domainId: string;
  domain: {
    id: string;
    domain: string;
    fullDomain: string;
    category: string;
    status: string;
  } | null;
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  notes: string;
  createdAt: string;
  updatedAt: string;
}

export default function ContactDataPage() {
  const { data: session } = useSession();
  const [contactDataList, setContactDataList] = useState<ContactDataWithDomain[]>([]);
  const [filteredContactData, setFilteredContactData] = useState<ContactDataWithDomain[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedContacts, setExpandedContacts] = useState<Set<string>>(new Set());

  // Funkcje eksportu
  const exportToCSV = (data: ContactDataWithDomain[]) => {
    const headers = [
      'Nazwa firmy',
      'Osoba kontaktowa',
      'Email',
      'Telefon',
      'Adres',
      'Domena',
      'Kategoria',
      'Status',
      'Notatki',
      'Data utworzenia'
    ];

    const csvData = data.map(contact => [
      contact.companyName,
      contact.contactPerson,
      contact.email,
      contact.phone,
      contact.address,
      contact.domain?.domain || '',
      contact.domain?.category || '',
      contact.domain?.status || '',
      contact.notes,
      formatDate(contact.createdAt)
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    downloadFile(csvContent, 'contact-data.csv', 'text/csv');
  };

  const exportToJSON = (data: ContactDataWithDomain[]) => {
    const jsonData = data.map(contact => ({
      companyName: contact.companyName,
      contactPerson: contact.contactPerson,
      email: contact.email,
      phone: contact.phone,
      address: contact.address,
      domain: contact.domain?.domain || '',
      category: contact.domain?.category || '',
      status: contact.domain?.status || '',
      notes: contact.notes,
      createdAt: contact.createdAt
    }));

    const jsonContent = JSON.stringify(jsonData, null, 2);
    downloadFile(jsonContent, 'contact-data.json', 'application/json');
  };

  const exportToXML = (data: ContactDataWithDomain[]) => {
    let xmlContent = '<?xml version="1.0" encoding="UTF-8"?>\n<contacts>\n';

    data.forEach(contact => {
      xmlContent += '  <contact>\n';
      xmlContent += `    <companyName>${escapeXml(contact.companyName)}</companyName>\n`;
      xmlContent += `    <contactPerson>${escapeXml(contact.contactPerson)}</contactPerson>\n`;
      xmlContent += `    <email>${escapeXml(contact.email)}</email>\n`;
      xmlContent += `    <phone>${escapeXml(contact.phone)}</phone>\n`;
      xmlContent += `    <address>${escapeXml(contact.address)}</address>\n`;
      xmlContent += `    <domain>${escapeXml(contact.domain?.domain || '')}</domain>\n`;
      xmlContent += `    <category>${escapeXml(contact.domain?.category || '')}</category>\n`;
      xmlContent += `    <status>${escapeXml(contact.domain?.status || '')}</status>\n`;
      xmlContent += `    <notes>${escapeXml(contact.notes)}</notes>\n`;
      xmlContent += `    <createdAt>${contact.createdAt}</createdAt>\n`;
      xmlContent += '  </contact>\n';
    });

    xmlContent += '</contacts>';
    downloadFile(xmlContent, 'contact-data.xml', 'application/xml');
  };

  const exportToExcel = (data: ContactDataWithDomain[]) => {
    // Simplified Excel format (actually TSV that Excel can open)
    const headers = [
      'Nazwa firmy',
      'Osoba kontaktowa',
      'Email',
      'Telefon',
      'Adres',
      'Domena',
      'Kategoria',
      'Status',
      'Notatki',
      'Data utworzenia'
    ];

    const excelData = data.map(contact => [
      contact.companyName,
      contact.contactPerson,
      contact.email,
      contact.phone,
      contact.address,
      contact.domain?.domain || '',
      contact.domain?.category || '',
      contact.domain?.status || '',
      contact.notes,
      formatDate(contact.createdAt)
    ]);

    const excelContent = [headers, ...excelData]
      .map(row => row.join('\t'))
      .join('\n');

    downloadFile(excelContent, 'contact-data.xls', 'application/vnd.ms-excel');
  };

  const escapeXml = (text: string) => {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  };

  const downloadFile = (content: string, fileName: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    toast.success(`Eksport do ${fileName} zakończony pomyślnie`);
  };

  const toggleExpanded = (contactId: string) => {
    setExpandedContacts(prev => {
      const newSet = new Set(prev);
      if (newSet.has(contactId)) {
        newSet.delete(contactId);
      } else {
        newSet.add(contactId);
      }
      return newSet;
    });
  };

  const fetchContactData = useCallback(async () => {
    if (!session?.user?.id) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/contact-data`);

      if (!response.ok) {
        throw new Error("Błąd pobierania danych kontaktowych");
      }

      const data = await response.json();
      setContactDataList(data.contactData || []);
      setFilteredContactData(data.contactData || []);
    } catch (error) {
      console.error("Błąd pobierania danych kontaktowych:", error);
      toast.error("Wystąpił błąd podczas pobierania danych kontaktowych");
    } finally {
      setIsLoading(false);
    }
  }, [session?.user?.id]);

  useEffect(() => {
    if (session?.user?.id) {
      fetchContactData();
    }
  }, [fetchContactData, session?.user?.id]);

  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredContactData(contactDataList);
      return;
    }

    const filtered = contactDataList.filter((contact) => {
      const searchLower = searchTerm.toLowerCase();
      return (
        contact.companyName.toLowerCase().includes(searchLower) ||
        contact.contactPerson.toLowerCase().includes(searchLower) ||
        contact.email.toLowerCase().includes(searchLower) ||
        contact.phone.toLowerCase().includes(searchLower) ||
        contact.domain?.domain.toLowerCase().includes(searchLower) ||
        contact.domain?.category.toLowerCase().includes(searchLower)
      );
    });

    setFilteredContactData(filtered);
  }, [searchTerm, contactDataList]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pl-PL", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      new: { color: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300", label: "Nowa" },
      in_progress: { color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300", label: "W trakcie" },
      accepted: { color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300", label: "Zaakceptowana" },
      rejected: { color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300", label: "Odrzucona" },
      closed: { color: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300", label: "Zamknięta" },
      in_audit: { color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300", label: "Trwa audyt" },
      production: { color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300", label: "Produkcyjna" },
      hold: { color: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300", label: "Wstrzymana" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.new;

    return (
      <Badge variant="outline" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">
          <p className="text-muted-foreground">
            Ładowanie danych kontaktowych...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dane kontaktowe</h1>
          <p className="text-muted-foreground">
            Wszystkie zebrane dane kontaktowe z domen
          </p>
        </div>
        <div className="flex items-center gap-2">
          <div className="text-sm text-muted-foreground">
            Łączna liczba: <strong>{filteredContactData.length}</strong>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Eksportuj
                <ChevronDown className="h-4 w-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => exportToCSV(filteredContactData)}>
                Eksportuj do CSV
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => exportToExcel(filteredContactData)}>
                Eksportuj do Excel
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => exportToJSON(filteredContactData)}>
                Eksportuj do JSON
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => exportToXML(filteredContactData)}>
                Eksportuj do XML
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Search and Filter Bar */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Wyszukiwanie i filtrowanie
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 md:flex-row">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Szukaj po nazwie firmy, osobie kontaktowej, emailu, telefonie lub domenie..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contact Data Grid */}
      {filteredContactData.length === 0 ? (
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <Database className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
              <h3 className="text-lg font-semibold mb-2">
                {searchTerm ? "Brak wyników wyszukiwania" : "Brak danych kontaktowych"}
              </h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm
                  ? "Nie znaleziono danych kontaktowych pasujących do wyszukiwania."
                  : "Jeszcze nie zebrano żadnych danych kontaktowych z domen."}
              </p>
              {!searchTerm && (
                <Button asChild>
                  <Link href="/dashboard/domains">
                    Przejdź do domen
                  </Link>
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredContactData.map((contact) => {
            const isExpanded = expandedContacts.has(contact.id);

            return (
              <Card key={contact.id} className="hover:shadow-md transition-shadow py-0">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3 flex-1">
                      <Building className="h-5 w-5 text-muted-foreground mt-1" />
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <div className="flex gap-4">
                            <h3 className="font-semibold text-lg">{contact.companyName}</h3>
                            {contact.domain && (
                              <div className="flex items-center gap-2 mt-1">
                                <Link
                                  href={`/dashboard/domains/${contact.domainId}`}
                                  className="text-primary hover:underline flex items-center gap-1 text-sm"
                                >
                                  <Globe className="h-3 w-3" />
                                  {contact.domain.domain}
                                </Link>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  asChild
                                  className="h-5 w-5 p-0"
                                >
                                  <Link
                                    href={contact.domain.fullDomain}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                  >
                                    <ExternalLink className="h-3 w-3" />
                                  </Link>
                                </Button>
                              </div>
                            )}
                          </div>

                          <div className="flex items-center gap-2">
                            <div className="flex items-center gap-2 mr-2">
                              <MapPin className="h-4 w-4 text-muted-foreground" />
                              <p className="font-medium text-sm">{contact.address}</p>
                            </div>
                            <Badge variant="secondary" className="text-xs">{contact.domain?.category}</Badge>
                            {contact.domain?.status && getStatusBadge(contact.domain.status)}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleExpanded(contact.id)}
                              className="ml-2"
                            >
                              {isExpanded ? (
                                <ChevronUp className="h-4 w-4" />
                              ) : (
                                <ChevronDown className="h-4 w-4" />
                              )}
                              <span className="ml-1 text-xs">
                                {isExpanded ? 'Mniej' : 'Więcej'}
                              </span>
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {isExpanded && (
                    <div className="mt-4 pt-4 border-t border-border">
                      <div className="flex justify-between">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-xs text-muted-foreground">Osoba kontaktowa</p>
                            <p className="font-medium text-sm">{contact.contactPerson}</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-xs text-muted-foreground">Email</p>
                            <p className="font-medium text-sm">{contact.email}</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-xs text-muted-foreground">Telefon</p>
                            <p className="font-medium text-sm">{contact.phone}</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-xs text-muted-foreground">Data utworzenia</p>
                            <p className="font-medium text-sm">{formatDate(contact.createdAt)}</p>
                          </div>
                        </div>


                      {contact.notes && (
                        <div className="lex items-center gap-2">
                          <FileText className="h-4 w-4 text-muted-foreground mt-0.5" />
                          <div className="flex-1">
                            <p className="text-xs text-muted-foreground">Notatki</p>
                            <p className="font-medium text-sm">{contact.notes}</p>
                          </div>
                        </div>
                      )}
                      </div>

                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
}