import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';

// GET endpoint do pobierania jednej najnowszej domeny bez danych kontaktowych
export async function GET(request: NextRequest) {
  try {
    // Pobierz userId z query parameters
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'userId parameter is required' },
        { status: 400 }
      );
    }

    await connectDB();

    // Znajdź najnowszą domenę bez danych kontaktowych
    const domain = await Domain.findOne({
      userId: userId,
      'metadata.hasContactData': { $exists: false } // nie ma danych kontaktowych
    })
    .sort({ createdAt: -1 }) // sortuj od najnowszej
    .lean(); // użyj lean() dla lepszej wydaj<PERSON>ści

    if (!domain) {
      return NextResponse.json(
        { success: true, domain: null, message: '<PERSON><PERSON> domen bez danych kontaktowych' },
        { status: 200 }
      );
    }

    return NextResponse.json({
      success: true,
      domain: {
        id: domain._id.toString(),
        domain: domain.domain,
        protocol: domain.protocol,
        fullDomain: domain.fullDomain,
        category: domain.category,
        linkCount: domain.linkCount,
        status: domain.status,
        cms: domain.cms,
        ocena: domain.ocena,
        hasContactData: domain.metadata?.hasContactData || null,
        createdAt: domain.createdAt,
        updatedAt: domain.updatedAt
      }
    });

  } catch (error) {
    console.error('Błąd pobierania domeny bez danych kontaktowych:', error);
    return NextResponse.json(
      { error: 'Błąd serwera' },
      { status: 500 }
    );
  }
}