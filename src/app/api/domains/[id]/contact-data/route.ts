import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import ContactData from '@/models/ContactData';
import Domain from '@/models/Domain';

// GET endpoint do pobierania danych kontaktowych dla konkretnej domeny
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    await connectDB();

    // Sprawdź czy domena istnieje
    const domain = await Domain.findOne({
      _id: id
    });

    if (!domain) {
      return NextResponse.json(
        { error: 'Domena nie została znaleziona' },
        { status: 404 }
      );
    }

    // Pobierz wszystkie dane kontaktowe dla tej domeny
    const contactDataList = await ContactData.find({
      domainId: id
    }).lean();

    if (!contactDataList || contactDataList.length === 0) {
      return NextResponse.json(
        { success: true, contactData: [], message: '<PERSON><PERSON> danych kontaktowych dla tej domeny' },
        { status: 200 }
      );
    }

    const formattedContacts = contactDataList.map(contact => ({
      id: contact._id.toString(),
      domainId: contact.domainId,
      companyName: contact.companyName,
      contactPerson: contact.contactPerson,
      email: contact.email,
      phone: contact.phone,
      address: contact.address,
      notes: contact.notes,
      createdAt: contact.createdAt,
      updatedAt: contact.updatedAt
    }));

    return NextResponse.json({
      success: true,
      contactData: formattedContacts
    });

  } catch (error) {
    console.error('Błąd pobierania danych kontaktowych domeny:', error);
    return NextResponse.json(
      { error: 'Błąd serwera' },
      { status: 500 }
    );
  }
}