import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    await connectDB();

    // Find the domain
    const domain = await Domain.findById(id);
    if (!domain) {
      return NextResponse.json(
        { success: false, error: 'Domena nie została znaleziona' },
        { status: 404 }
      );
    }

    // Get the email generation webhook URL from environment variables
    const generateEmailWebhookUrl = process.env.GENERATE_EMAIL_WEBHOOK_URL;
    if (!generateEmailWebhookUrl) {
      return NextResponse.json(
        { success: false, error: 'GENERATE_EMAIL_WEBHOOK_URL nie jest skonfigurowany' },
        { status: 500 }
      );
    }

    // Prepare the payload to send to the webhook
    const payload = {
      domainId: domain._id.toString(),
      domain: domain.domain,
      fullDomain: domain.fullDomain,
      category: domain.category,
      cms: domain.cms,
      status: domain.status,
      metadata: domain.metadata || {}
    };

    // Send POST request to the email generation webhook URL
    const webhookResponse = await fetch(generateEmailWebhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!webhookResponse.ok) {
      const errorText = await webhookResponse.text();
      console.error('Webhook response error:', errorText);
      return NextResponse.json(
        { success: false, error: 'Błąd podczas generowania emaila audytowego' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Generowanie emaila audytowego zostało rozpoczęte pomyślnie',
      domain: {
        id: domain._id,
        domain: domain.domain,
        fullDomain: domain.fullDomain,
        category: domain.category,
        cms: domain.cms,
        status: domain.status,
        metadata: domain.metadata
      }
    });

  } catch (error) {
    console.error('Błąd generowania emaila audytowego:', error);
    return NextResponse.json(
      { success: false, error: 'Wystąpił błąd podczas generowania emaila audytowego' },
      { status: 500 }
    );
  }
}