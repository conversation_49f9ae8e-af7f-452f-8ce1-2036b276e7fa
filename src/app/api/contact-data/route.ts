import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import ContactData from '@/models/ContactData';
import Domain from '@/models/Domain';

interface ContactDataInput {
  domainId: string;
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  notes?: string;
}

// GET endpoint do pobierania wszystkich danych kontaktowych użytkownika
export async function GET(request: NextRequest) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const contactDataList = await ContactData.find({})
    .sort({ createdAt: -1 })
    .lean();

    // Pobierz informacje o domenach dla każdego rekordu
    const domainIds = contactDataList.map(cd => cd.domainId);
    const domains = await Domain.find({
      _id: { $in: domainIds }
    }).lean();

    const domainsMap = new Map(domains.map(d => [d._id.toString(), d]));

    const enrichedContactData = contactDataList.map(cd => ({
      id: cd._id.toString(),
      domainId: cd.domainId,
      domain: domainsMap.get(cd.domainId),
      companyName: cd.companyName,
      contactPerson: cd.contactPerson,
      email: cd.email,
      phone: cd.phone,
      address: cd.address,
      notes: cd.notes,
      createdAt: cd.createdAt,
      updatedAt: cd.updatedAt
    }));

    return NextResponse.json({
      success: true,
      contactData: enrichedContactData
    });

  } catch (error) {
    console.error('Błąd pobierania danych kontaktowych:', error);
    return NextResponse.json(
      { error: 'Błąd serwera' },
      { status: 500 }
    );
  }
}

// POST endpoint do dodawania nowych danych kontaktowych (publiczny)
export async function POST(request: NextRequest) {
  try {
    const requestData = await request.json();
    
    // Sprawdź czy to tablica czy pojedynczy obiekt
    const contactDataInputs: ContactDataInput[] = Array.isArray(requestData) 
      ? requestData 
      : [requestData];

    // Walidacja danych dla każdego obiektu
    for (const contactDataInput of contactDataInputs) {
      if (!contactDataInput.domainId || !contactDataInput.companyName || 
          !contactDataInput.contactPerson || !contactDataInput.email || 
          !contactDataInput.phone || !contactDataInput.address) {
        return NextResponse.json(
          { error: 'Wszystkie wymagane pola muszą być wypełnione dla wszystkich obiektów (domainId, companyName, contactPerson, email, phone, address)' },
          { status: 400 }
        );
      }
    }

    await connectDB();

    const results = [];
    const errors = [];

    for (const contactDataInput of contactDataInputs) {
      try {
        // Sprawdź czy domena istnieje
        const domain = await Domain.findOne({
          _id: contactDataInput.domainId
        });

        if (!domain) {
          errors.push({
            domainId: contactDataInput.domainId,
            error: 'Domena nie została znaleziona'
          });
          continue;
        }

        // Sprawdź czy kontakt z tym samym emailem już istnieje dla tej domeny
        const existingContactData = await ContactData.findOne({
          domainId: contactDataInput.domainId,
          email: contactDataInput.email
        });

        if (existingContactData) {
          errors.push({
            domainId: contactDataInput.domainId,
            error: 'Kontakt z tym emailem już istnieje dla tej domeny'
          });
          continue;
        }

        // Utwórz nowe dane kontaktowe
        const newContactData = new ContactData({
          domainId: contactDataInput.domainId,
          companyName: contactDataInput.companyName,
          contactPerson: contactDataInput.contactPerson,
          email: contactDataInput.email,
          phone: contactDataInput.phone,
          address: contactDataInput.address,
          notes: contactDataInput.notes || ''
        });

        await newContactData.save();

        // Aktualizuj timestamp hasContactData w metadata
        await Domain.findByIdAndUpdate(
          contactDataInput.domainId,
          { 'metadata.hasContactData': new Date() }
        );

        results.push({
          id: newContactData._id.toString(),
          domainId: newContactData.domainId,
          companyName: newContactData.companyName,
          contactPerson: newContactData.contactPerson,
          email: newContactData.email,
          phone: newContactData.phone,
          address: newContactData.address,
          notes: newContactData.notes,
          createdAt: newContactData.createdAt,
          updatedAt: newContactData.updatedAt
        });

      } catch (itemError) {
        console.error('Błąd dodawania pojedynczego rekordu:', itemError);
        errors.push({
          domainId: contactDataInput.domainId,
          error: 'Błąd podczas dodawania rekordu'
        });
      }
    }

    // Zwróć wyniki
    const response: any = {
      success: results.length > 0,
      processed: contactDataInputs.length,
      successful: results.length,
      failed: errors.length
    };

    if (results.length > 0) {
      response.contactData = results;
      response.message = `Dodano ${results.length} z ${contactDataInputs.length} rekordów`;
    }

    if (errors.length > 0) {
      response.errors = errors;
    }

    const statusCode = results.length > 0 ? 201 : 400;
    return NextResponse.json(response, { status: statusCode });

  } catch (error) {
    console.error('Błąd dodawania danych kontaktowych:', error);
    
    return NextResponse.json(
      { error: 'Błąd serwera' },
      { status: 500 }
    );
  }
}