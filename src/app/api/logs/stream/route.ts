import { NextRequest } from 'next/server';
import connectDB from '@/lib/db';
import Log, { ILog } from '@/models/Log';
import { getServerSession } from "next-auth/next";
import { authOptions } from '@/lib/auth';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    await connectDB();

    const encoder = new TextEncoder();
    let isConnected = true;

    const stream = new ReadableStream({
      async start(controller) {
        // Send initial connection message
        controller.enqueue(
          encoder.encode(`data: ${JSON.stringify({ type: 'connected' })}\n\n`)
        );

        // Send initial logs
        const sendInitialLogs = async () => {
          try {
            const logs = await Log.find({})
              .sort({ createdAt: -1 })
              .limit(100)
              .lean();

            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify({ 
                type: 'initial', 
                logs,
                timestamp: new Date().toISOString()
              })}\n\n`)
            );
          } catch (error) {
            console.error('Error fetching initial logs for SSE:', error);
          }
        };

        await sendInitialLogs();

        // Set up MongoDB change stream to watch for new logs
        const changeStream = Log.watch([
          { $match: { operationType: 'insert' } }
        ]);

        changeStream.on('change', (change) => {
          if (!isConnected) return;
          
          if (change.operationType === 'insert') {
            const newLog: ILog = change.fullDocument as ILog;
            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify({ 
                type: 'new_log', 
                log: newLog,
                timestamp: new Date().toISOString()
              })}\n\n`)
            );
          }
        });

        changeStream.on('error', (error) => {
          console.error('Change Stream error:', error);
          if (isConnected) {
            controller.error(error);
          }
          changeStream.close();
        });

        // Send a keep-alive message every 30 seconds to prevent timeouts
        const keepAliveInterval = setInterval(() => {
          if (isConnected) {
            controller.enqueue(encoder.encode(': keep-alive\n\n'));
          }
        }, 30000);

        // Cleanup on client disconnect
        const cleanup = () => {
          isConnected = false;
          clearInterval(keepAliveInterval);
          changeStream.close();
          controller.close();
        };

        request.signal.addEventListener('abort', cleanup);
      },
      cancel() {
        isConnected = false;
        console.log('SSE stream cancelled.');
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      },
    });

  } catch (error) {
    console.error('Error setting up SSE stream:', error);
    return new Response(JSON.stringify({ error: 'Failed to set up SSE stream' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
}

