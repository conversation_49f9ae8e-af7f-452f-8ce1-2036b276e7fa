import { NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';
import Vulnerability from '@/models/Vulnerability';
import { getServerSession } from "next-auth/next";
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET() {
  const session = (await getServerSession(authOptions)) as Session | null;

  if (!session || !session.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    await connectDB();

    // Basic domain counts
    const totalDomains = await Domain.countDocuments({ userId: session.user.id });
    const newDomains = await Domain.countDocuments({ userId: session.user.id, status: 'new' });
    const acceptedDomains = await Domain.countDocuments({ userId: session.user.id, status: 'accepted' });
    const rejectedDomains = await Domain.countDocuments({ userId: session.user.id, status: 'rejected' });
    const inProgressDomains = await Domain.countDocuments({ userId: session.user.id, status: 'in_progress' });

    // CMS statistics
    const wordpressDomains = await Domain.countDocuments({ userId: session.user.id, cms: 'wordpress' });
    const otherCmsDomains = await Domain.countDocuments({ userId: session.user.id, cms: 'inny' });
    const unknownCmsDomains = await Domain.countDocuments({
      userId: session.user.id,
      $or: [{ cms: { $exists: false } }, { cms: null }]
    });

    // WordPress domains by category (for pie chart)
    const wordpressByCategory = await Domain.aggregate([
      { $match: { userId: session.user.id, cms: 'wordpress' } },
      { $group: { _id: '$category', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    // Get total vulnerabilities count
    const totalVulnerabilities = await Vulnerability.countDocuments();

    // Calculate total links count
    const linkStats = await Domain.aggregate([
      { $match: { userId: session.user.id } },
      { $group: { _id: null, totalLinks: { $sum: '$linkCount' } } }
    ]);
    const totalLinks = linkStats[0]?.totalLinks || 0;

    // Calculate average scan generation time (domains with audit content and dateScan in metadata)
    const scanTimeStats = await Domain.aggregate([
      {
        $match: {
          userId: session.user.id,
          auditContent: { $exists: true, $ne: null, $ne: '' },
          'metadata.dateScan': { $exists: true, $ne: null }
        }
      },
      {
        $addFields: {
          auditStartTime: {
            $dateFromString: {
              dateString: '$metadata.dateScan',
              onError: null
            }
          },
          auditEndTime: '$updatedAt'
        }
      },
      {
        $match: {
          auditStartTime: { $ne: null }
        }
      },
      {
        $addFields: {
          scanDuration: {
            $divide: [
              { $subtract: ['$auditEndTime', '$auditStartTime'] },
              1000 // Convert to seconds
            ]
          }
        }
      },
      {
        $group: {
          _id: null,
          averageScanTime: { $avg: '$scanDuration' },
          totalScans: { $sum: 1 }
        }
      }
    ]);

    const averageScanTime = scanTimeStats[0]?.averageScanTime || 0;
    const totalScansWithTime = scanTimeStats[0]?.totalScans || 0;

    // Pobierz 7 domen z najnowszą datą aktualizacji
    const recentDomains = await Domain.find({ userId: session.user.id })
      .sort({ updatedAt: -1 })
      .limit(7)
      .select('_id domain fullDomain category status cms updatedAt')
      .lean();

    const stats = {
      totalDomains,
      newDomains,
      acceptedDomains,
      rejectedDomains,
      inProgressDomains,
      wordpressDomains,
      otherCmsDomains,
      unknownCmsDomains,
      totalVulnerabilities,
      totalLinks,
      averageScanTime: Math.round(averageScanTime), // Round to whole seconds
      totalScansWithTime,
      wordpressByCategory: wordpressByCategory.map(item => ({
        category: item._id,
        count: item.count
      })),
      recentDomains: recentDomains.map(domain => ({
        id: domain._id,
        domain: domain.domain,
        fullDomain: domain.fullDomain,
        category: domain.category,
        status: domain.status,
        cms: domain.cms,
        updatedAt: domain.updatedAt
      })),
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
